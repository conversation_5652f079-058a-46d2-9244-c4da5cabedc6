// Dify API 配置文件示例
// 复制此文件为 config.js 并填入您的实际API密钥

const DIFY_CONFIG = {
    // API端点 - 您提供的Dify API地址
    API_URL: 'https://api.dify.ai/mcp/server/shSR4iUdPWyYKM0n/mcp',
    
    // API密钥 - 请替换为您的实际API密钥
    // 获取方式：登录Dify控制台 -> 应用设置 -> API密钥
    API_KEY: 'app-xxxxxxxxxxxxxxxxxxxxxxxxxx',
    
    // 应用配置
    APP_CONFIG: {
        // 用户标识前缀
        USER_PREFIX: 'ecobilder-user-',
        
        // 响应模式：
        // - 'blocking': 等待完整响应后返回
        // - 'streaming': 流式返回（实时显示）
        RESPONSE_MODE: 'blocking',
        
        // 最大消息长度（字符数）
        MAX_MESSAGE_LENGTH: 1000,
        
        // 请求超时时间（毫秒）
        REQUEST_TIMEOUT: 30000,
        
        // 网络错误时的最大重试次数
        MAX_RETRIES: 3
    },
    
    // UI界面配置
    UI_CONFIG: {
        // 聊天窗口标题
        CHAT_TITLE: 'AI生态专家 - EcoBilder',
        
        // 初始欢迎消息
        WELCOME_MESSAGE: '您好！我是AI生态专家，专注于生态建筑和可持续发展。我可以帮您解答关于绿色建筑、节能设计、环保材料、可再生能源等方面的问题。请问有什么可以帮助您的吗？',
        
        // 输入框提示文字
        INPUT_PLACEHOLDER: '请输入您关于生态建筑的问题...',
        
        // 各种错误提示消息
        ERROR_MESSAGES: {
            NETWORK_ERROR: '网络连接错误，请检查您的网络连接后重试',
            API_ERROR: 'API调用失败，请检查API配置或稍后重试',
            TIMEOUT_ERROR: '请求超时，服务器响应时间过长，请稍后重试',
            INVALID_RESPONSE: '服务器返回无效响应，请联系管理员',
            EMPTY_MESSAGE: '请输入消息内容',
            MESSAGE_TOO_LONG: `消息内容过长（超过1000字符），请缩短后重试`,
            API_KEY_ERROR: 'API密钥无效，请检查配置文件中的API_KEY设置',
            RATE_LIMIT_ERROR: '请求频率过高，请稍后再试'
        }
    },
    
    // 调试配置
    DEBUG_CONFIG: {
        // 是否在控制台输出调试信息
        ENABLE_CONSOLE_LOG: true,
        
        // 是否显示API请求详情
        LOG_API_REQUESTS: false,
        
        // 是否显示响应时间
        LOG_RESPONSE_TIME: true
    }
};

// 验证配置
function validateConfig() {
    if (!DIFY_CONFIG.API_KEY || DIFY_CONFIG.API_KEY === 'app-xxxxxxxxxxxxxxxxxxxxxxxxxx') {
        console.warn('⚠️ 警告: 请在config.js中设置正确的API密钥');
        return false;
    }
    
    if (!DIFY_CONFIG.API_URL) {
        console.error('❌ 错误: API_URL未设置');
        return false;
    }
    
    console.log('✅ 配置验证通过');
    return true;
}

// 导出配置（如果在模块环境中使用）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DIFY_CONFIG;
}

// 在浏览器环境中验证配置
if (typeof window !== 'undefined') {
    document.addEventListener('DOMContentLoaded', validateConfig);
}
