# EcoBilder - AI生态专家聊天界面

这是一个集成Dify API的现代化聊天界面，专为生态建筑和可持续发展咨询而设计。

## 功能特性

- 🤖 集成Dify AI API
- 💬 实时聊天界面
- 🎨 现代化UI设计
- 📱 响应式布局
- ⚡ 快速响应
- 🔄 自动重试机制
- ⏱️ 请求超时处理
- 🛡️ 错误处理

## 项目结构

```
ecobilder/
├── index.html          # 主聊天界面
├── config.js           # API配置文件
└── README.md           # 项目说明
```

## 快速开始

### 1. 配置API密钥

编辑 `config.js` 文件，将 `YOUR_API_KEY_HERE` 替换为您的实际Dify API密钥：

```javascript
const DIFY_CONFIG = {
    API_URL: 'https://api.dify.ai/mcp/server/shSR4iUdPWyYKM0n/mcp',
    API_KEY: 'YOUR_ACTUAL_API_KEY', // 替换为您的API密钥
    // ... 其他配置
};
```

### 2. 运行项目

#### 方法一：直接打开HTML文件
直接在浏览器中打开 `index.html` 文件即可使用。

#### 方法二：使用本地服务器（推荐）
```bash
# 使用Python启动本地服务器
python -m http.server 8000

# 或使用Node.js
npx http-server

# 然后在浏览器中访问 http://localhost:8000
```

### 3. 开始聊天

1. 在输入框中输入您关于生态建筑的问题
2. 点击"发送"按钮或按Enter键
3. AI将为您提供专业的回答

## 配置说明

### API配置
- `API_URL`: Dify API端点
- `API_KEY`: 您的API密钥

### 应用配置
- `USER_PREFIX`: 用户标识前缀
- `RESPONSE_MODE`: 响应模式（blocking/streaming）
- `MAX_MESSAGE_LENGTH`: 最大消息长度
- `REQUEST_TIMEOUT`: 请求超时时间
- `MAX_RETRIES`: 最大重试次数

### UI配置
- `CHAT_TITLE`: 聊天标题
- `WELCOME_MESSAGE`: 欢迎消息
- `INPUT_PLACEHOLDER`: 输入提示
- `ERROR_MESSAGES`: 各种错误消息

## 自定义

### 修改样式
编辑 `index.html` 中的CSS部分来自定义界面样式。

### 修改功能
编辑 `DifyChatClient` 类来添加新功能或修改现有行为。

### 修改配置
编辑 `config.js` 文件来调整API和UI配置。

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 故障排除

### 常见问题

1. **API调用失败**
   - 检查API密钥是否正确
   - 确认网络连接正常
   - 检查API端点是否可访问

2. **CORS错误**
   - 使用本地服务器运行项目
   - 确保API支持跨域请求

3. **消息发送失败**
   - 检查消息长度是否超限
   - 确认API配置正确

### 调试

打开浏览器开发者工具查看控制台输出，获取详细的错误信息。

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 联系

如有问题，请通过GitHub Issues联系我们。
