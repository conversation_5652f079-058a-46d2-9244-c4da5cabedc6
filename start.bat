@echo off
echo ========================================
echo    EcoBilder - AI生态专家聊天界面
echo ========================================
echo.

echo 正在启动本地服务器...
echo.

REM 检查Python是否可用
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo 使用Python启动服务器...
    echo 服务器地址: http://localhost:8000
    echo 按 Ctrl+C 停止服务器
    echo.
    start http://localhost:8000
    python -m http.server 8000
) else (
    REM 检查Node.js是否可用
    node --version >nul 2>&1
    if %errorlevel% == 0 (
        echo 使用Node.js启动服务器...
        echo 服务器地址: http://localhost:8080
        echo 按 Ctrl+C 停止服务器
        echo.
        start http://localhost:8080
        npx http-server -p 8080
    ) else (
        echo 警告: 未找到Python或Node.js
        echo 正在直接打开HTML文件...
        echo.
        start index.html
    )
)

pause
