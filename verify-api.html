<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dify API 验证工具</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
        }
        input, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Dify API 验证工具</h1>
        <p>这个工具会帮助您验证Dify API的配置和连接。</p>
        
        <div class="form-group">
            <label for="apiKey">API 密钥:</label>
            <input type="password" id="apiKey" placeholder="app-xxxxxxxxxx" value="app-ZRuKpcHUrE5E0zTtNQLddDCc">
        </div>
        
        <div class="form-group">
            <label for="apiUrl">API 端点:</label>
            <input type="text" id="apiUrl" value="https://api.dify.ai/v1/chat-messages">
        </div>
        
        <button onclick="verifyBasicInfo()">📋 验证基本信息</button>
        <button onclick="testConnection()">🌐 测试连接</button>
        <button onclick="testFormData()">📝 测试表单数据</button>
        
        <div id="result"></div>
    </div>

    <script>
        function showResult(message, type = 'info') {
            const result = document.getElementById('result');
            result.className = `result ${type}`;
            result.textContent = message;
        }

        function verifyBasicInfo() {
            const apiKey = document.getElementById('apiKey').value;
            const apiUrl = document.getElementById('apiUrl').value;
            
            let info = '=== 基本信息验证 ===\n\n';
            
            // 验证API密钥
            info += '🔑 API密钥检查:\n';
            if (!apiKey) {
                info += '❌ API密钥为空\n';
            } else if (!apiKey.startsWith('app-')) {
                info += '❌ API密钥格式错误（应该以"app-"开头）\n';
            } else if (apiKey.length < 30) {
                info += '⚠️ API密钥长度可能不正确\n';
            } else {
                info += '✅ API密钥格式正确\n';
            }
            info += `长度: ${apiKey.length} 字符\n`;
            info += `前缀: ${apiKey.substring(0, 10)}...\n\n`;
            
            // 验证API端点
            info += '🌐 API端点检查:\n';
            if (!apiUrl) {
                info += '❌ API端点为空\n';
            } else if (!apiUrl.startsWith('https://')) {
                info += '❌ API端点应该使用HTTPS\n';
            } else {
                info += '✅ API端点格式正确\n';
            }
            info += `端点: ${apiUrl}\n`;
            
            showResult(info, 'info');
        }

        async function testConnection() {
            const apiKey = document.getElementById('apiKey').value;
            const apiUrl = document.getElementById('apiUrl').value;
            
            if (!apiKey || !apiUrl) {
                showResult('请填写API密钥和端点', 'error');
                return;
            }
            
            showResult('正在测试连接...', 'info');
            
            try {
                // 使用最简单的请求格式
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${apiKey}`,
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        inputs: {},
                        query: "Hello",
                        response_mode: "blocking",
                        user: "test-user",
                        files: []
                    })
                });
                
                const responseText = await response.text();
                let result = `=== 连接测试结果 ===\n\n`;
                result += `状态码: ${response.status}\n`;
                result += `状态文本: ${response.statusText}\n`;
                result += `响应头: ${JSON.stringify(Object.fromEntries(response.headers), null, 2)}\n`;
                result += `响应体: ${responseText}\n`;
                
                if (response.ok) {
                    showResult(result, 'success');
                } else {
                    showResult(result, 'error');
                }
                
            } catch (error) {
                showResult(`连接失败: ${error.message}`, 'error');
            }
        }

        async function testFormData() {
            const apiKey = document.getElementById('apiKey').value;
            const apiUrl = document.getElementById('apiUrl').value;
            
            if (!apiKey || !apiUrl) {
                showResult('请填写API密钥和端点', 'error');
                return;
            }
            
            showResult('正在测试表单数据格式...', 'info');
            
            try {
                // 使用FormData格式
                const formData = new FormData();
                formData.append('inputs', '{}');
                formData.append('query', 'Hello');
                formData.append('response_mode', 'blocking');
                formData.append('user', 'test-user');
                
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${apiKey}`
                    },
                    body: formData
                });
                
                const responseText = await response.text();
                let result = `=== 表单数据测试结果 ===\n\n`;
                result += `状态码: ${response.status}\n`;
                result += `状态文本: ${response.statusText}\n`;
                result += `响应体: ${responseText}\n`;
                
                if (response.ok) {
                    showResult(result, 'success');
                } else {
                    showResult(result, 'error');
                }
                
            } catch (error) {
                showResult(`测试失败: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
