<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生态拓展专家 - AGI2B</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .chat-container {
            width: 90%;
            max-width: 900px;
            height: 90vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            color: white;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .service-icon {
            font-size: 2rem;
        }

        .service-info h1 {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .service-info p {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .header-actions {
            display: flex;
            gap: 10px;
        }

        .action-button {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background 0.3s;
        }

        .action-button:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
            line-height: 1.5;
        }

        .message.user .message-content {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            color: white;
            border-bottom-right-radius: 4px;
        }

        .message.assistant .message-content {
            background: white;
            color: #333;
            border: 1px solid #e0e0e0;
            border-bottom-left-radius: 4px;
        }

        .chat-input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #e0e0e0;
        }

        .input-group {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .chat-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            outline: none;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .chat-input:focus {
            border-color: #ff9a9e;
        }

        .send-button {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: opacity 0.3s;
        }

        .send-button:hover {
            opacity: 0.9;
        }

        .send-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .file-upload-area {
            margin-bottom: 15px;
            padding: 15px;
            border: 2px dashed #e0e0e0;
            border-radius: 10px;
            text-align: center;
            cursor: pointer;
            transition: border-color 0.3s;
        }

        .file-upload-area:hover {
            border-color: #ff9a9e;
        }

        .file-upload-area.dragover {
            border-color: #ff9a9e;
            background: rgba(255, 154, 158, 0.1);
        }

        .uploaded-files {
            margin-top: 10px;
        }

        .file-item {
            display: inline-block;
            background: #f0f0f0;
            padding: 5px 10px;
            border-radius: 15px;
            margin: 5px;
            font-size: 0.9rem;
        }

        .typing-indicator {
            display: none;
            padding: 10px 16px;
            background: white;
            border-radius: 18px;
            border: 1px solid #e0e0e0;
            max-width: 70px;
            margin-bottom: 15px;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dots span {
            width: 8px;
            height: 8px;
            background: #999;
            border-radius: 50%;
            animation: typing 1.4s infinite;
        }

        .typing-dots span:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-dots span:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typing {
            0%, 60%, 100% {
                transform: translateY(0);
            }
            30% {
                transform: translateY(-10px);
            }
        }

        .error-message {
            background: #ffebee;
            color: #c62828;
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #c62828;
        }

        .knowledge-status {
            background: rgba(255, 154, 158, 0.1);
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 15px;
            font-size: 0.9rem;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <div class="header-left">
                <div class="service-icon">🌱</div>
                <div class="service-info">
                    <h1>生态拓展专家</h1>
                    <p>智能分析市场生态，构建合作伙伴网络</p>
                </div>
            </div>
            <div class="header-actions">
                <button class="action-button" onclick="goBack()">← 返回</button>
                <button class="action-button" onclick="clearChat()">清空对话</button>
            </div>
        </div>
        
        <div class="chat-messages" id="chatMessages">
            <div class="knowledge-status">
                📚 生态拓展知识库已加载 | 🤖 RAG智能检索已启用
            </div>
            
            <div class="message assistant">
                <div class="message-content">
                    您好！我是生态拓展专家，专注于帮助企业分析市场生态、构建合作伙伴网络、制定生态战略。
                    
                    我可以帮助您：
                    • 分析行业生态格局
                    • 识别潜在合作伙伴
                    • 制定生态拓展策略
                    • 评估生态合作风险
                    • 优化生态合作模式
                    
                    请上传相关文档或直接提问，我将基于专业知识库为您提供精准建议！
                </div>
            </div>
        </div>

        <div class="typing-indicator" id="typingIndicator">
            <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>

        <div class="chat-input-container">
            <div class="file-upload-area" id="fileUploadArea">
                📎 点击或拖拽上传文档到知识库 (支持PDF、Word、TXT等格式)
                <input type="file" id="fileInput" style="display: none;" multiple accept=".pdf,.doc,.docx,.txt,.md">
                <div class="uploaded-files" id="uploadedFiles"></div>
            </div>
            
            <div class="input-group">
                <input 
                    type="text" 
                    class="chat-input" 
                    id="chatInput" 
                    placeholder="请输入您的生态拓展问题..."
                    maxlength="1000"
                >
                <button class="send-button" id="sendButton">发送</button>
            </div>
        </div>
    </div>

    <!-- 引入配置文件和工作流客户端 -->
    <script src="services-config.js"></script>
    <script src="dify-workflow-client.js"></script>

    <script>
        // 生态拓展工作流客户端
        class EcologyWorkflowClient {
            constructor() {
                this.config = null;
                this.workflowClient = null;
                this.isInitialized = false;
                this.uploadedFiles = [];

                this.initializeConfig();
            }

            // 初始化配置
            initializeConfig() {
                try {
                    if (typeof SERVICES_CONFIG !== 'undefined' && SERVICES_CONFIG.ecology) {
                        this.config = SERVICES_CONFIG.ecology;

                        // 验证配置
                        const validation = this.validateConfig();
                        if (validation.isValid) {
                            this.workflowClient = new DifyWorkflowClient(this.config);
                            this.isInitialized = true;
                            console.log('✅ 生态拓展工作流客户端初始化成功');
                            this.updateUIStatus('ready');
                        } else {
                            console.warn('⚠️ 工作流配置验证失败:', validation.errors);
                            this.updateUIStatus('config-error', validation.errors);
                        }
                    } else {
                        console.error('❌ 未找到生态拓展服务配置');
                        this.updateUIStatus('config-missing');
                    }
                } catch (error) {
                    console.error('❌ 工作流客户端初始化失败:', error);
                    this.updateUIStatus('init-error', error.message);
                }
            }

            // 验证配置
            validateConfig() {
                const errors = [];

                if (!this.config.apiKey || this.config.apiKey.includes('your-')) {
                    errors.push('API密钥未配置');
                }

                if (!this.config.apiKey.startsWith('app-')) {
                    errors.push('API密钥格式错误（应以app-开头）');
                }

                if (!this.config.workflowId || this.config.workflowId.includes('your-')) {
                    errors.push('工作流ID未配置');
                }

                return {
                    isValid: errors.length === 0,
                    errors: errors
                };
            }

            // 发送消息到工作流
            async sendMessage(message) {
                if (!this.isInitialized) {
                    throw new Error('工作流客户端未初始化');
                }

                try {
                    // 构建额外输入参数
                    const additionalInputs = {};

                    // 如果有上传的文件，添加文件信息
                    if (this.uploadedFiles.length > 0) {
                        additionalInputs.uploaded_files = this.uploadedFiles.map(file => ({
                            name: file.name,
                            type: file.type,
                            size: file.size,
                            content: file.content || '文件内容待处理'
                        }));
                    }

                    // 调用工作流
                    const result = await this.workflowClient.runWorkflow(message, additionalInputs);

                    return this.formatWorkflowResponse(result);

                } catch (error) {
                    console.error('🚨 工作流执行失败:', error);
                    throw new Error(`工作流执行失败: ${error.message}`);
                }
            }

            // 格式化工作流响应
            formatWorkflowResponse(result) {
                let formattedResponse = result.answer;

                // 如果有分析结果，添加到响应中
                if (result.analysis) {
                    formattedResponse += '\n\n📊 **分析结果:**\n' + result.analysis;
                }

                // 如果有建议，添加到响应中
                if (result.recommendations) {
                    formattedResponse += '\n\n💡 **建议:**\n' + result.recommendations;
                }

                // 添加执行元数据（调试用）
                if (result.execution_metadata && result.execution_metadata.total_tokens > 0) {
                    formattedResponse += `\n\n🔧 *执行信息: 消耗${result.execution_metadata.total_tokens}个token，耗时${result.execution_metadata.execution_time}ms*`;
                }

                return formattedResponse;
            }

            // 更新UI状态
            updateUIStatus(status, details = null) {
                const knowledgeStatus = document.querySelector('.knowledge-status');
                if (!knowledgeStatus) return;

                switch (status) {
                    case 'ready':
                        knowledgeStatus.innerHTML = '📚 生态拓展知识库已加载 | 🔄 Dify工作流已就绪 | 🤖 RAG智能检索已启用';
                        knowledgeStatus.style.background = 'rgba(76, 175, 80, 0.1)';
                        break;
                    case 'config-error':
                        knowledgeStatus.innerHTML = `⚠️ 工作流配置错误: ${details.join(', ')} | 请检查配置文件`;
                        knowledgeStatus.style.background = 'rgba(255, 193, 7, 0.1)';
                        break;
                    case 'config-missing':
                        knowledgeStatus.innerHTML = '❌ 未找到工作流配置 | 请检查 services-config.js 文件';
                        knowledgeStatus.style.background = 'rgba(244, 67, 54, 0.1)';
                        break;
                    case 'init-error':
                        knowledgeStatus.innerHTML = `❌ 工作流初始化失败: ${details}`;
                        knowledgeStatus.style.background = 'rgba(244, 67, 54, 0.1)';
                        break;
                    case 'processing':
                        knowledgeStatus.innerHTML = '🔄 工作流执行中... | 🤖 AI正在深度分析';
                        knowledgeStatus.style.background = 'rgba(33, 150, 243, 0.1)';
                        break;
                }
            }

            // 添加上传文件
            addUploadedFile(file) {
                this.uploadedFiles.push(file);
                console.log('📎 文件已添加到工作流上下文:', file.name);
            }

            // 清除上传文件
            clearUploadedFiles() {
                this.uploadedFiles = [];
                console.log('🗑️ 已清除工作流文件上下文');
            }

            // 重置工作流会话
            resetWorkflow() {
                if (this.workflowClient) {
                    this.workflowClient.resetConversation();
                }
                this.clearUploadedFiles();
                console.log('🔄 工作流会话已重置');
            }

            // 获取工作流状态
            getStatus() {
                if (!this.workflowClient) {
                    return { status: 'not_initialized' };
                }
                return {
                    status: 'initialized',
                    ...this.workflowClient.getStatus(),
                    uploadedFiles: this.uploadedFiles.length
                };
            }
        }

        // 全局工作流客户端实例
        let ecologyClient = null;

        // 初始化聊天功能
        function initChat() {
            const chatInput = document.getElementById('chatInput');
            const sendButton = document.getElementById('sendButton');

            // 初始化工作流客户端
            ecologyClient = new EcologyWorkflowClient();

            sendButton.addEventListener('click', sendMessage);
            chatInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });
        }

        // 发送消息
        async function sendMessage() {
            const chatInput = document.getElementById('chatInput');
            const sendButton = document.getElementById('sendButton');
            const message = chatInput.value.trim();

            if (!message) return;

            // 禁用输入
            chatInput.disabled = true;
            sendButton.disabled = true;

            // 添加用户消息
            addMessage(message, 'user');
            chatInput.value = '';

            // 显示打字指示器和处理状态
            showTypingIndicator();
            if (ecologyClient) {
                ecologyClient.updateUIStatus('processing');
            }

            try {
                let response;

                if (ecologyClient && ecologyClient.isInitialized) {
                    // 使用工作流
                    response = await ecologyClient.sendMessage(message);
                } else {
                    // 降级到模拟响应
                    await new Promise(resolve => setTimeout(resolve, 1500));
                    response = generateFallbackResponse(message);
                }

                hideTypingIndicator();
                addMessage(response, 'assistant');

                // 恢复就绪状态
                if (ecologyClient) {
                    ecologyClient.updateUIStatus('ready');
                }

            } catch (error) {
                hideTypingIndicator();
                console.error('❌ 消息发送失败:', error);

                const errorMessage = `抱歉，处理您的请求时出现了问题：${error.message}\n\n请检查网络连接和配置，或稍后重试。`;
                addMessage(errorMessage, 'assistant');

                // 显示错误状态
                if (ecologyClient) {
                    ecologyClient.updateUIStatus('config-error', [error.message]);
                }
            } finally {
                // 恢复输入
                chatInput.disabled = false;
                sendButton.disabled = false;
                chatInput.focus();
            }
        }

        // 降级响应生成（当工作流不可用时）
        function generateFallbackResponse(message) {
            const lowerMessage = message.toLowerCase();

            if (lowerMessage.includes('生态') || lowerMessage.includes('合作')) {
                return "🌱 **生态分析建议**\n\n基于您的问题，我建议从以下几个维度分析生态拓展机会：\n\n1. **上下游产业链整合** - 识别关键供应商和渠道伙伴\n2. **技术生态伙伴合作** - 寻找互补技术能力的合作伙伴\n3. **渠道生态建设** - 构建多元化的销售和服务网络\n\n💡 *注意：当前使用模拟响应，请配置Dify工作流以获得更专业的分析结果。*";
            } else if (lowerMessage.includes('市场') || lowerMessage.includes('竞争')) {
                return "📊 **市场生态分析**\n\n从市场生态角度看，建议重点关注：\n\n• **生态位定位** - 明确自身在生态中的核心价值\n• **竞争格局分析** - 识别直接和间接竞争对手\n• **差异化策略** - 制定独特的生态合作模式\n• **风险评估** - 评估生态合作的潜在风险\n\n💡 *注意：当前使用模拟响应，请配置Dify工作流以获得更深度的市场分析。*";
            } else {
                return "🤖 **生态拓展咨询**\n\n感谢您的咨询！作为生态拓展专家，我可以帮助您：\n\n• 分析行业生态格局和机会\n• 识别潜在的合作伙伴\n• 制定生态拓展策略和路线图\n• 评估生态合作的风险和收益\n• 优化现有的生态合作模式\n\n请提供更具体的问题或上传相关文档，我将为您提供更精准的建议。\n\n💡 *注意：当前使用模拟响应，请配置Dify工作流以获得基于专业知识库的分析。*";
            }
        }

        // 添加消息到聊天界面
        function addMessage(content, type) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            contentDiv.textContent = content;
            
            messageDiv.appendChild(contentDiv);
            chatMessages.appendChild(messageDiv);
            
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // 显示/隐藏打字指示器
        function showTypingIndicator() {
            document.getElementById('typingIndicator').style.display = 'block';
        }

        function hideTypingIndicator() {
            document.getElementById('typingIndicator').style.display = 'none';
        }

        // 文件上传功能
        function initFileUpload() {
            const fileUploadArea = document.getElementById('fileUploadArea');
            const fileInput = document.getElementById('fileInput');
            const uploadedFiles = document.getElementById('uploadedFiles');

            fileUploadArea.addEventListener('click', () => fileInput.click());
            
            fileInput.addEventListener('change', handleFileUpload);
            
            // 拖拽上传
            fileUploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                fileUploadArea.classList.add('dragover');
            });
            
            fileUploadArea.addEventListener('dragleave', () => {
                fileUploadArea.classList.remove('dragover');
            });
            
            fileUploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                fileUploadArea.classList.remove('dragover');
                handleFileUpload({ target: { files: e.dataTransfer.files } });
            });
        }

        // 处理文件上传
        function handleFileUpload(event) {
            const files = event.target.files;
            const uploadedFiles = document.getElementById('uploadedFiles');

            for (let file of files) {
                // 验证文件大小和类型
                if (file.size > 10 * 1024 * 1024) { // 10MB限制
                    addMessage(`❌ 文件 "${file.name}" 超过10MB大小限制`, 'assistant');
                    continue;
                }

                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.textContent = `📄 ${file.name}`;
                uploadedFiles.appendChild(fileItem);

                // 添加到工作流客户端
                if (ecologyClient) {
                    ecologyClient.addUploadedFile({
                        name: file.name,
                        type: file.type,
                        size: file.size,
                        lastModified: file.lastModified
                    });
                }

                console.log('📎 文件已添加到工作流上下文:', file.name);

                // 显示文件上传成功消息
                addMessage(`✅ 文件 "${file.name}" 已上传到知识库，可以在对话中引用此文件内容。`, 'assistant');
            }
        }

        // 返回服务列表
        function goBack() {
            window.location.href = 'services.html';
        }

        // 清空对话
        function clearChat() {
            const chatMessages = document.getElementById('chatMessages');
            const uploadedFiles = document.getElementById('uploadedFiles');

            // 重置工作流会话
            if (ecologyClient) {
                ecologyClient.resetWorkflow();
            }

            // 清空上传文件显示
            uploadedFiles.innerHTML = '';

            // 重置聊天界面
            chatMessages.innerHTML = `
                <div class="knowledge-status">
                    📚 生态拓展知识库已加载 | 🔄 Dify工作流已就绪 | 🤖 RAG智能检索已启用
                </div>

                <div class="message assistant">
                    <div class="message-content">
                        您好！我是生态拓展专家，基于Dify智能工作流为您提供专业的生态分析服务。

                        🔄 工作流功能：
                        • 智能分析市场生态格局
                        • 识别潜在合作伙伴
                        • 制定生态拓展策略
                        • 评估生态合作风险
                        • 生成生态合作建议

                        请上传相关文档或直接提问，我将通过专业的AI工作流为您提供深度分析！
                    </div>
                </div>
            `;

            // 更新状态
            if (ecologyClient && ecologyClient.isInitialized) {
                ecologyClient.updateUIStatus('ready');
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            initChat();
            initFileUpload();
        });
    </script>
</body>
</html>
