/**
 * AGI2B Dify工作流集成验证脚本
 * 全面验证工作流集成的各个方面
 */

const { SERVICES_CONFIG } = require('./services-config');
const DifyWorkflowClient = require('./dify-workflow-client');

async function verifyIntegration() {
    console.log('🔍 AGI2B Dify工作流集成验证\n');
    console.log('=' .repeat(50));
    
    let allTestsPassed = true;
    const testResults = [];
    
    // 测试1: 配置验证
    console.log('\n📋 测试1: 配置验证');
    try {
        const config = SERVICES_CONFIG.ecology;
        
        console.log(`✓ 服务名称: ${config.name}`);
        console.log(`✓ API URL: ${config.apiUrl}`);
        console.log(`✓ 工作流ID: ${config.workflowId}`);
        console.log(`✓ API密钥: ${config.apiKey.substring(0, 10)}...`);
        
        // 验证配置完整性
        const requiredFields = ['apiKey', 'apiUrl', 'workflowId'];
        for (const field of requiredFields) {
            if (!config[field]) {
                throw new Error(`缺少必需配置: ${field}`);
            }
        }
        
        // 验证API密钥格式
        if (!config.apiKey.startsWith('app-')) {
            throw new Error('API密钥格式错误，应以 app- 开头');
        }
        
        console.log('✅ 配置验证通过');
        testResults.push({ test: '配置验证', status: 'PASS' });
        
    } catch (error) {
        console.log(`❌ 配置验证失败: ${error.message}`);
        testResults.push({ test: '配置验证', status: 'FAIL', error: error.message });
        allTestsPassed = false;
    }
    
    // 测试2: 客户端初始化
    console.log('\n🔧 测试2: 客户端初始化');
    let workflowClient;
    try {
        workflowClient = new DifyWorkflowClient(SERVICES_CONFIG.ecology);
        console.log('✅ 工作流客户端初始化成功');
        testResults.push({ test: '客户端初始化', status: 'PASS' });
        
    } catch (error) {
        console.log(`❌ 客户端初始化失败: ${error.message}`);
        testResults.push({ test: '客户端初始化', status: 'FAIL', error: error.message });
        allTestsPassed = false;
        return; // 无法继续后续测试
    }
    
    // 测试3: API连接测试
    console.log('\n🌐 测试3: API连接测试');
    try {
        const testQuery = '测试连接';
        console.log(`发送测试查询: ${testQuery}`);
        
        const startTime = Date.now();
        const response = await workflowClient.callWorkflowAPI(testQuery);
        const endTime = Date.now();
        
        console.log(`✅ API连接成功，耗时: ${endTime - startTime}ms`);
        testResults.push({ test: 'API连接', status: 'PASS', duration: endTime - startTime });
        
        // 测试4: 响应解析
        console.log('\n📊 测试4: 响应解析');
        try {
            const parsedResult = workflowClient.parseWorkflowResponse(response);
            
            console.log('✅ 响应解析成功');
            console.log(`- 回答长度: ${parsedResult.answer.length} 字符`);
            console.log(`- 会话ID: ${parsedResult.conversation_id || '无'}`);
            console.log(`- 工作流ID: ${parsedResult.workflow_id || '无'}`);
            
            testResults.push({ test: '响应解析', status: 'PASS' });
            
            // 显示部分响应内容
            if (parsedResult.answer) {
                console.log('\n📝 工作流响应预览:');
                console.log('-'.repeat(40));
                console.log(parsedResult.answer.substring(0, 200) + (parsedResult.answer.length > 200 ? '...' : ''));
                console.log('-'.repeat(40));
            }
            
        } catch (parseError) {
            console.log(`❌ 响应解析失败: ${parseError.message}`);
            testResults.push({ test: '响应解析', status: 'FAIL', error: parseError.message });
            allTestsPassed = false;
        }
        
    } catch (apiError) {
        console.log(`❌ API连接失败: ${apiError.message}`);
        testResults.push({ test: 'API连接', status: 'FAIL', error: apiError.message });
        allTestsPassed = false;
        
        // 提供具体的错误建议
        if (apiError.message.includes('401')) {
            console.log('\n🔑 API密钥问题建议:');
            console.log('1. 检查API密钥是否正确');
            console.log('2. 确认工作流是否已发布');
            console.log('3. 验证API密钥权限');
            console.log('4. 尝试重新生成API密钥');
        }
    }
    
    // 测试结果汇总
    console.log('\n' + '='.repeat(50));
    console.log('📊 测试结果汇总');
    console.log('='.repeat(50));
    
    testResults.forEach((result, index) => {
        const status = result.status === 'PASS' ? '✅' : '❌';
        const duration = result.duration ? ` (${result.duration}ms)` : '';
        console.log(`${index + 1}. ${result.test}: ${status} ${result.status}${duration}`);
        
        if (result.error) {
            console.log(`   错误: ${result.error}`);
        }
    });
    
    console.log('\n' + '='.repeat(50));
    if (allTestsPassed) {
        console.log('🎉 所有测试通过! Dify工作流集成已成功配置');
        console.log('\n✨ 下一步:');
        console.log('1. 工作流已准备就绪，可以在AGI2B系统中使用');
        console.log('2. 访问 http://localhost:8000/workflow_setup.html 查看集成状态');
        console.log('3. 开始使用生态拓展专家服务');
        
    } else {
        console.log('⚠️ 部分测试失败，请根据上述错误信息进行修复');
        console.log('\n🔧 修复建议:');
        console.log('1. 运行 "node update-api-key.js" 更新API密钥');
        console.log('2. 检查Dify控制台中的工作流状态');
        console.log('3. 确认网络连接正常');
        console.log('4. 查看 dify-setup-guide.md 获取详细指导');
    }
    
    console.log('\n📞 需要帮助?');
    console.log('- 查看设置指南: dify-setup-guide.md');
    console.log('- 运行更新工具: node update-api-key.js');
    console.log('- 重新测试: node verify-integration.js');
    
    return allTestsPassed;
}

// 运行验证
if (require.main === module) {
    verifyIntegration()
        .then(success => {
            process.exit(success ? 0 : 1);
        })
        .catch(error => {
            console.error('❌ 验证过程中发生异常:', error);
            process.exit(1);
        });
}

module.exports = { verifyIntegration };
