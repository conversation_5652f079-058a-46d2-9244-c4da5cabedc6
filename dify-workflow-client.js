/**
 * Dify工作流客户端
 * 专门处理Dify Workflow API的调用
 */
class DifyWorkflowClient {
    constructor(config) {
        this.config = config;
        this.apiKey = config.apiKey;
        this.apiUrl = config.apiUrl;
        this.workflowId = config.workflowId;
        this.workflowInputs = config.workflowInputs || {};
        this.workflowOutputs = config.workflowOutputs || {};
        this.conversationId = null;
        this.retryCount = 0;
        this.maxRetries = config.maxRetries || 3;
        this.requestTimeout = config.requestTimeout || 30000;
    }

    /**
     * 运行工作流
     * @param {string} query - 用户输入
     * @param {Object} additionalInputs - 额外的输入参数
     * @returns {Promise<Object>} 工作流执行结果
     */
    async runWorkflow(query, additionalInputs = {}) {
        try {
            console.log('🔄 开始执行Dify工作流...');
            console.log('Query:', query);
            console.log('Additional Inputs:', additionalInputs);

            const response = await this.callWorkflowAPI(query, additionalInputs);
            
            if (response && response.data) {
                console.log('✅ 工作流执行成功:', response);
                return this.parseWorkflowResponse(response);
            } else {
                throw new Error('工作流响应格式错误');
            }
        } catch (error) {
            console.error('❌ 工作流执行失败:', error);
            
            // 重试逻辑
            if (this.retryCount < this.maxRetries) {
                this.retryCount++;
                console.log(`🔄 重试第 ${this.retryCount} 次...`);
                await this.delay(1000 * this.retryCount);
                return this.runWorkflow(query, additionalInputs);
            }
            
            throw error;
        }
    }

    /**
     * 调用Dify工作流API
     * @param {string} query - 用户输入
     * @param {Object} additionalInputs - 额外输入参数
     * @returns {Promise<Object>} API响应
     */
    async callWorkflowAPI(query, additionalInputs = {}) {
        // 构建工作流输入参数
        const inputs = {
            [this.workflowInputs.query || 'query']: query,
            ...additionalInputs
        };

        // 如果有上下文信息，添加到输入中
        if (this.conversationId && this.workflowInputs.context) {
            inputs[this.workflowInputs.context] = this.conversationId;
        }

        // 添加用户信息
        if (this.workflowInputs.user_info) {
            inputs[this.workflowInputs.user_info] = {
                user_id: 'user_' + Date.now(),
                session_id: this.conversationId || 'session_' + Date.now()
            };
        }

        const requestBody = {
            inputs: inputs,
            response_mode: 'blocking', // 阻塞模式，等待完整结果
            user: 'ecology_user_' + Date.now()
        };

        console.log('📤 发送工作流请求:', requestBody);

        // 创建带超时的fetch请求
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.requestTimeout);

        try {
            const response = await fetch(this.apiUrl, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'User-Agent': 'AGI2B-EcologyExpert/1.0'
                },
                body: JSON.stringify(requestBody),
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                const errorText = await response.text();
                console.error('🚨 工作流API响应错误:', response.status, response.statusText);
                console.error('错误详情:', errorText);
                throw new Error(`工作流API错误: ${response.status} - ${response.statusText}. 详情: ${errorText}`);
            }

            const responseData = await response.json();
            console.log('📥 工作流API响应成功:', responseData);
            return responseData;

        } catch (error) {
            clearTimeout(timeoutId);
            if (error.name === 'AbortError') {
                throw new Error('工作流请求超时');
            }
            console.error('🚨 工作流API请求异常:', error);
            throw error;
        }
    }

    /**
     * 解析工作流响应
     * @param {Object} response - 工作流API响应
     * @returns {Object} 解析后的结果
     */
    parseWorkflowResponse(response) {
        try {
            const data = response.data;
            const outputs = data.outputs || {};
            
            // 提取主要结果
            const result = outputs[this.workflowOutputs.result || 'result'] || 
                          outputs.answer || 
                          outputs.output || 
                          '工作流执行完成，但未返回结果';

            // 提取分析结果（如果有）
            const analysis = outputs[this.workflowOutputs.analysis || 'analysis'] || null;
            
            // 提取建议（如果有）
            const recommendations = outputs[this.workflowOutputs.recommendations || 'recommendations'] || null;

            // 更新会话ID
            if (data.conversation_id) {
                this.conversationId = data.conversation_id;
            }

            // 构建返回结果
            const parsedResult = {
                answer: result,
                analysis: analysis,
                recommendations: recommendations,
                conversation_id: this.conversationId,
                workflow_id: data.workflow_id || this.workflowId,
                execution_metadata: {
                    total_tokens: data.metadata?.usage?.total_tokens || 0,
                    execution_time: data.metadata?.execution_time || 0,
                    steps_executed: data.metadata?.steps || 0
                },
                raw_outputs: outputs // 保留原始输出用于调试
            };

            console.log('✨ 工作流结果解析完成:', parsedResult);
            return parsedResult;

        } catch (error) {
            console.error('🚨 工作流响应解析失败:', error);
            throw new Error('工作流响应解析失败: ' + error.message);
        }
    }

    /**
     * 流式运行工作流（如果支持）
     * @param {string} query - 用户输入
     * @param {Function} onChunk - 处理流式数据的回调函数
     * @param {Object} additionalInputs - 额外输入参数
     */
    async runWorkflowStream(query, onChunk, additionalInputs = {}) {
        const inputs = {
            [this.workflowInputs.query || 'query']: query,
            ...additionalInputs
        };

        const requestBody = {
            inputs: inputs,
            response_mode: 'streaming', // 流式模式
            user: 'ecology_user_' + Date.now()
        };

        console.log('📤 发送流式工作流请求:', requestBody);

        try {
            const response = await fetch(this.apiUrl, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json',
                    'Accept': 'text/event-stream',
                    'User-Agent': 'AGI2B-EcologyExpert/1.0'
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                throw new Error(`流式工作流API错误: ${response.status}`);
            }

            const reader = response.body.getReader();
            const decoder = new TextDecoder();

            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                const chunk = decoder.decode(value);
                const lines = chunk.split('\n');

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        try {
                            const data = JSON.parse(line.slice(6));
                            onChunk(data);
                        } catch (e) {
                            console.warn('解析流式数据失败:', line);
                        }
                    }
                }
            }

        } catch (error) {
            console.error('🚨 流式工作流执行失败:', error);
            throw error;
        }
    }

    /**
     * 重置会话
     */
    resetConversation() {
        this.conversationId = null;
        this.retryCount = 0;
        console.log('🔄 工作流会话已重置');
    }

    /**
     * 获取工作流状态
     */
    getStatus() {
        return {
            apiKey: this.apiKey ? `${this.apiKey.substring(0, 10)}...` : 'Not set',
            workflowId: this.workflowId,
            conversationId: this.conversationId,
            retryCount: this.retryCount,
            isConfigured: !!(this.apiKey && this.workflowId)
        };
    }

    /**
     * 延迟函数
     * @param {number} ms - 延迟毫秒数
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 验证配置
     */
    validateConfig() {
        const errors = [];
        
        if (!this.apiKey || this.apiKey.includes('your-')) {
            errors.push('API密钥未配置');
        }
        
        if (!this.apiKey.startsWith('app-')) {
            errors.push('API密钥格式错误（应以app-开头）');
        }
        
        if (!this.workflowId || this.workflowId.includes('your-')) {
            errors.push('工作流ID未配置');
        }
        
        if (!this.apiUrl || !this.apiUrl.includes('workflows/run')) {
            errors.push('工作流API端点配置错误');
        }
        
        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }
}

// 导出类（用于浏览器环境）
if (typeof window !== 'undefined') {
    window.DifyWorkflowClient = DifyWorkflowClient;
}

// 导出类（用于Node.js环境）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DifyWorkflowClient;
}
