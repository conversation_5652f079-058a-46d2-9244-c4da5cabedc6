/**
 * 快速修复API密钥工具
 * 提供多种方式来更新和测试API密钥
 */

const fs = require('fs');
const https = require('https');
const { URL } = require('url');

// 测试API密钥是否有效
function testApiKey(apiKey) {
    return new Promise((resolve) => {
        const requestBody = JSON.stringify({
            inputs: { query: "test" },
            response_mode: "blocking",
            user: "test_user"
        });
        
        const options = {
            hostname: 'api.dify.ai',
            port: 443,
            path: '/v1/workflows/run',
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${apiKey}`,
                'Content-Type': 'application/json'
            },
            timeout: 15000
        };
        
        const req = https.request(options, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                resolve({
                    valid: res.statusCode === 200,
                    status: res.statusCode,
                    response: data
                });
            });
        });
        
        req.on('error', () => {
            resolve({ valid: false, status: 'ERROR', response: 'Network error' });
        });
        
        req.on('timeout', () => {
            req.destroy();
            resolve({ valid: false, status: 'TIMEOUT', response: 'Request timeout' });
        });
        
        req.write(requestBody);
        req.end();
    });
}

// 更新配置文件中的API密钥
function updateConfigFile(newApiKey, newWorkflowId = null) {
    try {
        const configPath = './services-config.js';
        let content = fs.readFileSync(configPath, 'utf8');
        
        // 更新API密钥
        content = content.replace(
            /apiKey:\s*'[^']*'/,
            `apiKey: '${newApiKey}'`
        );
        
        // 如果提供了新的工作流ID，也更新它
        if (newWorkflowId) {
            content = content.replace(
                /workflowId:\s*'[^']*'/,
                `workflowId: '${newWorkflowId}'`
            );
        }
        
        fs.writeFileSync(configPath, content, 'utf8');
        return true;
    } catch (error) {
        console.error('更新配置文件失败:', error.message);
        return false;
    }
}

async function main() {
    console.log('🔧 Dify API密钥快速修复工具\n');
    
    // 显示当前状态
    console.log('📋 当前问题诊断:');
    console.log('✅ 网络连接正常');
    console.log('✅ API端点正确 (https://api.dify.ai/v1/workflows/run)');
    console.log('❌ API密钥无效 (401 Unauthorized)');
    console.log('');
    
    console.log('🔑 API密钥问题的可能原因:');
    console.log('1. API密钥不完整或复制错误');
    console.log('2. API密钥已过期');
    console.log('3. 工作流未发布或权限不足');
    console.log('4. 使用了错误的API密钥类型');
    console.log('');
    
    console.log('💡 解决方案:');
    console.log('');
    
    console.log('方案1: 从Dify控制台重新获取API密钥');
    console.log('--------------------------------------');
    console.log('1. 访问: https://cloud.dify.ai');
    console.log('2. 进入您的应用');
    console.log('3. 点击左侧菜单 "API访问" 或 "API管理"');
    console.log('4. 找到或创建新的API密钥');
    console.log('5. 复制完整的密钥（应该比当前的更长）');
    console.log('');
    
    console.log('方案2: 检查工作流状态');
    console.log('--------------------');
    console.log('1. 在Dify控制台中打开工作流');
    console.log('2. 确认工作流状态为"已发布"');
    console.log('3. 检查工作流的输入输出配置');
    console.log('4. 确认API密钥有访问该工作流的权限');
    console.log('');
    
    console.log('方案3: 使用测试工具验证');
    console.log('----------------------');
    console.log('获取新密钥后，可以使用以下命令测试:');
    console.log('');
    console.log('# 测试新的API密钥');
    console.log('curl -X POST "https://api.dify.ai/v1/workflows/run" \\');
    console.log('  -H "Authorization: Bearer YOUR_NEW_API_KEY" \\');
    console.log('  -H "Content-Type: application/json" \\');
    console.log('  -d \'{"inputs":{"query":"test"},"response_mode":"blocking","user":"test"}\'');
    console.log('');
    
    console.log('📝 手动更新配置:');
    console.log('如果您已经有了新的API密钥，可以直接编辑 services-config.js 文件:');
    console.log('');
    console.log('找到这一行:');
    console.log('  apiKey: \'app-ZRuKpcHUrE5E6zTNQLddDCc\',');
    console.log('');
    console.log('替换为:');
    console.log('  apiKey: \'您的新API密钥\',');
    console.log('');
    
    console.log('🚀 完成后运行测试:');
    console.log('node verify-integration.js');
    console.log('');
    
    // 提供一个简单的API密钥测试功能
    console.log('🧪 如果您想测试一个新的API密钥，请输入它:');
    console.log('(直接按回车跳过测试)');
    
    const readline = require('readline');
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });
    
    rl.question('请输入要测试的API密钥: ', async (testKey) => {
        if (testKey && testKey.trim()) {
            console.log('\n🔍 测试API密钥...');
            const result = await testApiKey(testKey.trim());
            
            if (result.valid) {
                console.log('✅ API密钥有效!');
                
                rl.question('是否要更新配置文件? (y/n): ', (answer) => {
                    if (answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes') {
                        if (updateConfigFile(testKey.trim())) {
                            console.log('✅ 配置文件已更新!');
                            console.log('🚀 现在可以运行: node verify-integration.js');
                        }
                    }
                    rl.close();
                });
            } else {
                console.log(`❌ API密钥无效 (状态: ${result.status})`);
                if (result.response) {
                    try {
                        const errorData = JSON.parse(result.response);
                        console.log(`错误信息: ${errorData.message}`);
                    } catch (e) {
                        console.log(`错误信息: ${result.response}`);
                    }
                }
                console.log('请检查API密钥是否正确，或按照上述方案重新获取。');
                rl.close();
            }
        } else {
            console.log('跳过测试。请按照上述方案获取正确的API密钥。');
            rl.close();
        }
    });
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = { testApiKey, updateConfigFile };
