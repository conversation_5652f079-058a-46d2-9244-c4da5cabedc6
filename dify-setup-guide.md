# Dify工作流集成设置指南

## 🔧 当前状态
✅ 网络连接正常  
✅ API端点正确  
❌ API密钥无效  

## 📋 需要的信息
根据您的截图，我们需要以下信息：
- **工作流ID**: `8cc0dc2f-37ed-4dbd-b647-0fc278810788` ✅
- **API密钥**: 需要重新获取 ❌

## 🔑 获取正确的API密钥

### 步骤1: 访问Dify控制台
1. 打开 [Dify控制台](https://cloud.dify.ai)
2. 登录您的账户
3. 选择包含工作流的应用

### 步骤2: 获取API密钥
1. 在应用页面，点击左侧菜单的 **"API访问"** 或 **"API管理"**
2. 在API密钥部分，您会看到现有的密钥
3. 如果当前密钥无效，点击 **"创建密钥"** 或 **"重新生成"**
4. 复制完整的API密钥（应该以 `app-` 开头，长度约40-50个字符）

### 步骤3: 验证工作流状态
1. 在应用中，点击 **"工作流"** 菜单
2. 找到您的工作流（ID: `8cc0dc2f-37ed-4dbd-b647-0fc278810788`）
3. 确保工作流状态为 **"已发布"**
4. 检查工作流的输入和输出参数配置

## 🚀 更新配置

### 方法1: 使用更新工具
```bash
node update-api-key.js
```

### 方法2: 手动更新
编辑 `services-config.js` 文件，更新以下行：
```javascript
apiKey: '您的新API密钥', // 替换为从Dify控制台获取的完整密钥
```

## 🧪 测试配置

### 运行完整测试
```bash
node test-dify-workflow.js
```

### 运行简化测试
```bash
node simple-dify-test.js
```

### 运行HTTPS测试
```bash
node https-dify-test.js
```

## 🔍 常见问题

### Q: API密钥格式是什么？
A: 正确的API密钥格式：`app-xxxxxxxxxxxxxxxxxxxxxxxxx`
- 以 `app-` 开头
- 总长度约40-50个字符
- 包含字母和数字

### Q: 为什么会出现401错误？
A: 可能的原因：
1. API密钥不完整或错误
2. API密钥已过期
3. 工作流未发布
4. API密钥权限不足

### Q: 如何确认工作流已发布？
A: 在Dify控制台中：
1. 进入工作流编辑页面
2. 检查右上角是否显示"已发布"状态
3. 如果未发布，点击"发布"按钮

## 📞 下一步

1. **获取正确的API密钥**：按照上述步骤从Dify控制台获取
2. **更新配置**：使用 `node update-api-key.js` 或手动编辑配置文件
3. **测试连接**：运行 `node test-dify-workflow.js` 验证配置
4. **集成到应用**：配置成功后，工作流将自动集成到AGI2B系统中

## 🎯 预期结果

配置成功后，您应该看到：
```
✅ API调用成功!
📊 工作流响应: {...}
🎉 测试完成 - 工作流集成正常工作!
```

---

**需要帮助？** 请提供：
1. 从Dify控制台获取的完整API密钥
2. 工作流的发布状态截图
3. 任何错误信息的完整输出
