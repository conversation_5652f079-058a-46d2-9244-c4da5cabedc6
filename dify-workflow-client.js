/**
 * Dify工作流客户端
 * 专门处理Dify Workflow API的调用
 */
class DifyWorkflowClient {
    constructor(config) {
        this.config = config;
        this.apiKey = config.apiKey;
        this.apiUrl = config.apiUrl;
        this.workflowId = config.workflowId;
        this.workflowInputs = config.workflowInputs || {};
        this.workflowOutputs = config.workflowOutputs || {};
        this.conversationId = null;
        this.retryCount = 0;
        this.maxRetries = config.maxRetries || 3;
        this.requestTimeout = config.requestTimeout || 30000;
    }

    /**
     * 运行工作流
     * @param {string} query - 用户输入
     * @param {Object} additionalInputs - 额外的输入参数
     * @returns {Promise<Object>} 工作流执行结果
     */
    async runWorkflow(query, additionalInputs = {}) {
        try {
            console.log('🔄 开始执行Dify工作流...');
            console.log('Query:', query);
            console.log('Additional Inputs:', additionalInputs);

            const response = await this.callWorkflowAPI(query, additionalInputs);
            
            if (response && response.data) {
                console.log('✅ 工作流执行成功:', response);
                return this.parseWorkflowResponse(response);
            } else {
                throw new Error('工作流响应格式错误');
            }
        } catch (error) {
            console.error('❌ 工作流执行失败:', error);
            
            // 重试逻辑
            if (this.retryCount < this.maxRetries) {
                this.retryCount++;
                console.log(`🔄 重试第 ${this.retryCount} 次...`);
                await this.delay(1000 * this.retryCount);
                return this.runWorkflow(query, additionalInputs);
            }
            
            throw error;
        }
    }

    /**
     * 调用Dify工作流API
     * @param {string} query - 用户输入
     * @param {Object} additionalInputs - 额外输入参数
     * @returns {Promise<Object>} API响应
     */
    async callWorkflowAPI(query, additionalInputs = {}) {
        // 构建工作流输入参数 - 根据Dify工作流API规范
        const inputs = {
            // 主要查询参数
            query: query
        };

        // 添加额外输入参数
        Object.keys(additionalInputs).forEach(key => {
            inputs[key] = additionalInputs[key];
        });

        // 如果有上传的文件，转换为字符串格式
        if (additionalInputs.uploaded_files && Array.isArray(additionalInputs.uploaded_files)) {
            inputs.uploaded_files = JSON.stringify(additionalInputs.uploaded_files);
        }

        // 构建请求体 - 符合Dify工作流API格式
        const requestBody = {
            inputs: inputs,
            response_mode: 'blocking', // 阻塞模式，等待完整结果
            user: 'ecology_user_' + Date.now(),
            files: [] // 如果需要上传文件，在这里添加
        };

        console.log('📤 发送工作流请求:', JSON.stringify(requestBody, null, 2));

        // 创建带超时的fetch请求
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.requestTimeout);

        try {
            const response = await fetch(this.apiUrl, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'User-Agent': 'AGI2B-EcologyExpert/1.0'
                },
                body: JSON.stringify(requestBody),
                signal: controller.signal
            });

            clearTimeout(timeoutId);
            console.log(`📡 API响应状态: ${response.status} ${response.statusText}`);

            const responseText = await response.text();
            console.log('📥 原始响应:', responseText);

            if (!response.ok) {
                console.error('🚨 工作流API响应错误:', response.status, response.statusText);
                console.error('错误详情:', responseText);

                // 提供更详细的错误信息
                let errorMessage = `工作流API错误: ${response.status} - ${response.statusText}`;

                try {
                    const errorData = JSON.parse(responseText);
                    if (errorData.message) {
                        errorMessage += `. ${errorData.message}`;
                    }

                    // 根据错误类型提供建议
                    if (response.status === 401) {
                        errorMessage += '\n建议: 检查API密钥是否正确，确认工作流是否已发布';
                    } else if (response.status === 404) {
                        errorMessage += '\n建议: 检查工作流ID是否正确，确认工作流是否存在';
                    } else if (response.status === 429) {
                        errorMessage += '\n建议: API调用频率过高，请稍后重试';
                    }
                } catch (e) {
                    errorMessage += `. 原始错误: ${responseText}`;
                }

                throw new Error(errorMessage);
            }

            // 尝试解析JSON响应
            let responseData;
            try {
                responseData = JSON.parse(responseText);
            } catch (e) {
                console.error('⚠️ 响应不是有效的JSON格式:', responseText);
                throw new Error('服务器返回了无效的JSON响应');
            }

            console.log('📥 工作流API响应成功:', JSON.stringify(responseData, null, 2));
            return responseData;

        } catch (error) {
            clearTimeout(timeoutId);
            if (error.name === 'AbortError') {
                throw new Error('工作流请求超时，请检查网络连接或增加超时时间');
            }
            console.error('🚨 工作流API请求异常:', error);
            throw error;
        }
    }

    /**
     * 解析工作流响应
     * @param {Object} response - 工作流API响应
     * @returns {Object} 解析后的结果
     */
    parseWorkflowResponse(response) {
        try {
            console.log('🔍 解析工作流响应:', JSON.stringify(response, null, 2));

            // Dify工作流响应格式处理
            const data = response.data || response;
            const outputs = data.outputs || {};

            // 提取主要结果 - 根据Dify工作流的输出格式
            let result = '';
            if (outputs.text) {
                result = outputs.text;
            } else if (outputs.answer) {
                result = outputs.answer;
            } else if (outputs.result) {
                result = outputs.result;
            } else if (outputs.output) {
                result = outputs.output;
            } else {
                // 如果没有找到标准输出，尝试获取第一个字符串类型的输出
                const outputKeys = Object.keys(outputs);
                for (const key of outputKeys) {
                    if (typeof outputs[key] === 'string' && outputs[key].trim()) {
                        result = outputs[key];
                        break;
                    }
                }

                if (!result) {
                    result = '工作流执行完成，但未返回文本结果';
                }
            }

            // 提取分析结果（如果有）
            const analysis = outputs.analysis || outputs.detailed_analysis || null;

            // 提取建议（如果有）
            const recommendations = outputs.recommendations || outputs.suggestions || null;

            // 更新会话ID
            if (data.conversation_id) {
                this.conversationId = data.conversation_id;
            }

            // 构建返回结果
            const parsedResult = {
                answer: result,
                analysis: analysis,
                recommendations: recommendations,
                conversation_id: this.conversationId,
                workflow_id: data.workflow_id || this.workflowId,
                execution_metadata: {
                    total_tokens: data.metadata?.usage?.total_tokens || 0,
                    execution_time: data.metadata?.execution_time || 0,
                    steps_executed: data.metadata?.steps || 0,
                    workflow_run_id: data.workflow_run_id || null
                },
                raw_outputs: outputs // 保留原始输出用于调试
            };

            console.log('✨ 工作流结果解析完成:', parsedResult);
            return parsedResult;

        } catch (error) {
            console.error('🚨 工作流响应解析失败:', error);
            throw new Error('工作流响应解析失败: ' + error.message);
        }
    }

    /**
     * 流式运行工作流（如果支持）
     * @param {string} query - 用户输入
     * @param {Function} onChunk - 处理流式数据的回调函数
     * @param {Object} additionalInputs - 额外输入参数
     */
    async runWorkflowStream(query, onChunk, additionalInputs = {}) {
        const inputs = {
            [this.workflowInputs.query || 'query']: query,
            ...additionalInputs
        };

        const requestBody = {
            inputs: inputs,
            response_mode: 'streaming', // 流式模式
            user: 'ecology_user_' + Date.now()
        };

        console.log('📤 发送流式工作流请求:', requestBody);

        try {
            const response = await fetch(this.apiUrl, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json',
                    'Accept': 'text/event-stream',
                    'User-Agent': 'AGI2B-EcologyExpert/1.0'
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                throw new Error(`流式工作流API错误: ${response.status}`);
            }

            const reader = response.body.getReader();
            const decoder = new TextDecoder();

            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                const chunk = decoder.decode(value);
                const lines = chunk.split('\n');

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        try {
                            const data = JSON.parse(line.slice(6));
                            onChunk(data);
                        } catch (e) {
                            console.warn('解析流式数据失败:', line);
                        }
                    }
                }
            }

        } catch (error) {
            console.error('🚨 流式工作流执行失败:', error);
            throw error;
        }
    }

    /**
     * 重置会话
     */
    resetConversation() {
        this.conversationId = null;
        this.retryCount = 0;
        console.log('🔄 工作流会话已重置');
    }

    /**
     * 获取工作流状态
     */
    getStatus() {
        return {
            apiKey: this.apiKey ? `${this.apiKey.substring(0, 10)}...` : 'Not set',
            workflowId: this.workflowId,
            conversationId: this.conversationId,
            retryCount: this.retryCount,
            isConfigured: !!(this.apiKey && this.workflowId)
        };
    }

    /**
     * 延迟函数
     * @param {number} ms - 延迟毫秒数
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 验证配置
     */
    validateConfig() {
        const errors = [];
        
        if (!this.apiKey || this.apiKey.includes('your-')) {
            errors.push('API密钥未配置');
        }
        
        if (!this.apiKey.startsWith('app-')) {
            errors.push('API密钥格式错误（应以app-开头）');
        }
        
        if (!this.workflowId || this.workflowId.includes('your-')) {
            errors.push('工作流ID未配置');
        }
        
        if (!this.apiUrl || !this.apiUrl.includes('workflows/run')) {
            errors.push('工作流API端点配置错误');
        }
        
        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }
}

// 导出类（用于浏览器环境）
if (typeof window !== 'undefined') {
    window.DifyWorkflowClient = DifyWorkflowClient;
}

// 导出类（用于Node.js环境）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DifyWorkflowClient;
}
