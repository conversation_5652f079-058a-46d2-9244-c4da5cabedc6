<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EcoBilder 配置向导</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .setup-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            max-width: 600px;
            width: 100%;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #333;
            margin-bottom: 10px;
        }
        .header p {
            color: #666;
            font-size: 16px;
        }
        .step {
            margin-bottom: 25px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #4285f4;
        }
        .step h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .step p {
            margin: 0 0 10px 0;
            color: #666;
            line-height: 1.5;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        input:focus {
            outline: none;
            border-color: #4285f4;
        }
        .btn {
            background: #4285f4;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: background 0.3s;
            width: 100%;
        }
        .btn:hover {
            background: #3367d6;
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            display: none;
        }
        .success {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            color: #2e7d32;
        }
        .error {
            background: #ffebee;
            border: 1px solid #f44336;
            color: #c62828;
        }
        .link {
            color: #4285f4;
            text-decoration: none;
        }
        .link:hover {
            text-decoration: underline;
        }
        .code {
            background: #f5f5f5;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 13px;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="header">
            <h1>🌱 EcoBilder 配置向导</h1>
            <p>让我们来设置您的Dify API密钥，开始使用AI生态专家</p>
        </div>

        <div class="step">
            <h3>📋 步骤 1: 获取API密钥</h3>
            <p>1. 访问 <a href="https://cloud.dify.ai" target="_blank" class="link">Dify控制台</a></p>
            <p>2. 登录您的账户</p>
            <p>3. 选择您的应用或创建新应用</p>
            <p>4. 在左侧菜单中点击"API访问"</p>
            <p>5. 复制API密钥（格式类似：<span class="code">app-xxxxxxxxxx</span>）</p>
        </div>

        <div class="step">
            <h3>🔑 步骤 2: 输入API密钥</h3>
            <div class="form-group">
                <label for="apiKey">API密钥:</label>
                <input type="password" id="apiKey" placeholder="app-xxxxxxxxxx" />
            </div>
            <button class="btn" onclick="testAndSave()">🚀 测试并保存配置</button>
        </div>

        <div id="result" class="result"></div>

        <div class="step" style="margin-top: 30px;">
            <h3>🛠️ 其他工具</h3>
            <p>
                <a href="debug.html" target="_blank" class="link">🔧 调试工具</a> - 
                用于测试API连接和排查问题
            </p>
            <p>
                <a href="index.html" class="link">💬 返回聊天界面</a> - 
                配置完成后开始使用
            </p>
        </div>
    </div>

    <script>
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = message;
            resultDiv.style.display = 'block';
        }

        async function testAndSave() {
            const apiKey = document.getElementById('apiKey').value.trim();
            
            if (!apiKey) {
                showResult('请输入API密钥', 'error');
                return;
            }
            
            if (!apiKey.startsWith('app-')) {
                showResult('API密钥格式错误，应该以 "app-" 开头', 'error');
                return;
            }
            
            showResult('正在测试API连接...', 'info');
            
            try {
                const response = await fetch('https://api.dify.ai/v1/chat-messages', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${apiKey}`
                    },
                    body: JSON.stringify({
                        inputs: {},
                        query: '你好',
                        response_mode: 'blocking',
                        user: 'setup-test-' + Date.now()
                    })
                });
                
                if (response.ok) {
                    // API测试成功，生成配置文件内容
                    const configContent = generateConfigFile(apiKey);
                    
                    showResult(`
                        ✅ <strong>API测试成功！</strong><br><br>
                        请按以下步骤完成配置：<br>
                        1. 打开 <code>config.js</code> 文件<br>
                        2. 找到 <code>API_KEY: 'YOUR_API_KEY_HERE'</code><br>
                        3. 将 <code>YOUR_API_KEY_HERE</code> 替换为：<code>${apiKey}</code><br>
                        4. 保存文件<br><br>
                        <a href="index.html" class="link">🎉 现在可以开始使用聊天界面了！</a>
                    `, 'success');
                } else {
                    const errorData = await response.text();
                    showResult(`❌ API测试失败<br>状态码: ${response.status}<br>错误信息: ${errorData}`, 'error');
                }
            } catch (error) {
                showResult(`❌ 网络错误: ${error.message}<br><br>可能的原因：<br>1. 网络连接问题<br>2. API密钥无效<br>3. CORS策略限制`, 'error');
            }
        }
        
        function generateConfigFile(apiKey) {
            return `// 已通过配置向导验证的API密钥
const DIFY_CONFIG = {
    API_URL: 'https://api.dify.ai/v1/chat-messages',
    API_KEY: '${apiKey}',
    // ... 其他配置保持不变
};`;
        }
    </script>
</body>
</html>
