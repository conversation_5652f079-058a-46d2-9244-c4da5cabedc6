<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dify工作流配置向导 - 生态拓展</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .content {
            padding: 30px;
        }

        .step {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            background: #f8f9fa;
        }

        .step-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .step-number {
            background: #ff9a9e;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9rem;
            font-weight: bold;
        }

        .step-content {
            line-height: 1.6;
            color: #555;
        }

        .code-block {
            background: #f1f3f4;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            overflow-x: auto;
        }

        .highlight {
            background: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: 600;
        }

        .form-group {
            margin: 20px 0;
        }

        .form-label {
            display: block;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .form-input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-input:focus {
            outline: none;
            border-color: #ff9a9e;
        }

        .form-input.valid {
            border-color: #28a745;
        }

        .form-input.invalid {
            border-color: #dc3545;
        }

        .validation-message {
            font-size: 0.8rem;
            margin-top: 5px;
        }

        .validation-message.success {
            color: #28a745;
        }

        .validation-message.error {
            color: #dc3545;
        }

        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            margin: 10px;
        }

        .btn-primary {
            background: #ff9a9e;
            color: white;
        }

        .btn-primary:hover {
            background: #ff8a8e;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #218838;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }

        .alert-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }

        .alert-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }

        .alert-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .workflow-preview {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .workflow-node {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            position: relative;
        }

        .workflow-node::before {
            content: '↓';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            background: white;
            color: #6c757d;
            font-size: 1.2rem;
        }

        .workflow-node:last-child::before {
            display: none;
        }

        .node-title {
            font-weight: 600;
            color: #495057;
            margin-bottom: 5px;
        }

        .node-description {
            font-size: 0.9rem;
            color: #6c757d;
        }

        .button-group {
            text-align: center;
            margin-top: 30px;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
            }
            
            .content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 Dify工作流配置向导</h1>
            <p>配置生态拓展专家的智能工作流</p>
        </div>

        <div class="content">
            <div class="alert alert-info">
                <strong>📋 配置说明：</strong> 生态拓展服务使用Dify工作流来提供更专业的分析能力。请按照以下步骤配置您的工作流。
            </div>

            <div class="step">
                <div class="step-title">
                    <div class="step-number">1</div>
                    创建Dify工作流应用
                </div>
                <div class="step-content">
                    <p>1. 登录 <a href="https://cloud.dify.ai" target="_blank">Dify控制台</a></p>
                    <p>2. 点击"创建应用" → 选择"工作流"</p>
                    <p>3. 选择"从空白开始"或使用模板</p>
                    <p>4. 为应用命名，例如："生态拓展分析工作流"</p>
                </div>
            </div>

            <div class="step">
                <div class="step-title">
                    <div class="step-number">2</div>
                    设计工作流结构
                </div>
                <div class="step-content">
                    <p>建议的工作流结构：</p>
                    <div class="workflow-preview">
                        <div class="workflow-node">
                            <div class="node-title">🎯 开始节点</div>
                            <div class="node-description">接收用户输入的生态拓展问题</div>
                        </div>
                        <div class="workflow-node">
                            <div class="node-title">🧠 LLM节点</div>
                            <div class="node-description">分析问题类型和意图</div>
                        </div>
                        <div class="workflow-node">
                            <div class="node-title">📚 知识检索节点</div>
                            <div class="node-description">从知识库检索相关信息</div>
                        </div>
                        <div class="workflow-node">
                            <div class="node-title">🔍 条件判断节点</div>
                            <div class="node-description">根据问题类型选择分析路径</div>
                        </div>
                        <div class="workflow-node">
                            <div class="node-title">📊 分析节点</div>
                            <div class="node-description">生成生态分析报告</div>
                        </div>
                        <div class="workflow-node">
                            <div class="node-title">💡 建议生成节点</div>
                            <div class="node-description">基于分析结果生成建议</div>
                        </div>
                        <div class="workflow-node">
                            <div class="node-title">📤 结束节点</div>
                            <div class="node-description">输出最终结果</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="step">
                <div class="step-title">
                    <div class="step-number">3</div>
                    配置输入输出参数
                </div>
                <div class="step-content">
                    <p><strong>输入参数配置：</strong></p>
                    <div class="code-block">
query (文本): 用户的生态拓展问题
context (文本, 可选): 对话上下文
user_info (对象, 可选): 用户信息
uploaded_files (数组, 可选): 上传的文件信息
                    </div>
                    
                    <p><strong>输出参数配置：</strong></p>
                    <div class="code-block">
result (文本): 主要分析结果
analysis (文本, 可选): 详细分析报告
recommendations (文本, 可选): 具体建议
                    </div>
                </div>
            </div>

            <div class="step">
                <div class="step-title">
                    <div class="step-number">4</div>
                    获取API密钥和工作流ID
                </div>
                <div class="step-content">
                    <p>1. 在工作流应用中，点击右上角"发布"</p>
                    <p>2. 进入"API访问"页面</p>
                    <p>3. 复制 <span class="highlight">API密钥</span> 和 <span class="highlight">工作流ID</span></p>
                    
                    <div class="form-group">
                        <label class="form-label">API密钥：</label>
                        <input type="text" class="form-input" id="apiKey" 
                               placeholder="app-xxxxxxxxxx" 
                               onchange="validateApiKey(this.value)">
                        <div class="validation-message" id="apiKeyMessage"></div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">工作流ID：</label>
                        <input type="text" class="form-input" id="workflowId" 
                               placeholder="xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx" 
                               onchange="validateWorkflowId(this.value)">
                        <div class="validation-message" id="workflowIdMessage"></div>
                    </div>
                </div>
            </div>

            <div class="step">
                <div class="step-title">
                    <div class="step-number">5</div>
                    测试工作流
                </div>
                <div class="step-content">
                    <p>配置完成后，您可以测试工作流是否正常工作：</p>
                    <div class="button-group">
                        <button class="btn btn-primary" onclick="testWorkflow()">🧪 测试工作流</button>
                        <button class="btn btn-secondary" onclick="generateConfig()">📄 生成配置</button>
                        <button class="btn btn-success" onclick="goToEcologyChat()" id="goToChatBtn" style="display: none;">🚀 开始使用</button>
                    </div>
                    <div id="testResult"></div>
                </div>
            </div>

            <div class="alert alert-warning">
                <strong>⚠️ 注意事项：</strong>
                <ul style="margin-top: 10px; margin-left: 20px;">
                    <li>确保工作流已正确发布并可以访问</li>
                    <li>API密钥需要有工作流执行权限</li>
                    <li>建议先在Dify控制台测试工作流</li>
                    <li>配置完成后需要更新 services-config.js 文件</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        let configValid = {
            apiKey: false,
            workflowId: false
        };

        // 验证API密钥
        function validateApiKey(apiKey) {
            const input = document.getElementById('apiKey');
            const message = document.getElementById('apiKeyMessage');

            if (!apiKey || apiKey.trim() === '') {
                input.className = 'form-input';
                message.textContent = '';
                configValid.apiKey = false;
                return;
            }

            if (!apiKey.startsWith('app-')) {
                input.className = 'form-input invalid';
                message.textContent = '❌ API密钥格式错误（应以 app- 开头）';
                message.className = 'validation-message error';
                configValid.apiKey = false;
            } else if (apiKey.length < 30) {
                input.className = 'form-input invalid';
                message.textContent = '⚠️ API密钥长度可能不正确';
                message.className = 'validation-message error';
                configValid.apiKey = false;
            } else {
                input.className = 'form-input valid';
                message.textContent = '✅ API密钥格式正确';
                message.className = 'validation-message success';
                configValid.apiKey = true;
            }

            updateButtons();
        }

        // 验证工作流ID
        function validateWorkflowId(workflowId) {
            const input = document.getElementById('workflowId');
            const message = document.getElementById('workflowIdMessage');

            if (!workflowId || workflowId.trim() === '') {
                input.className = 'form-input';
                message.textContent = '';
                configValid.workflowId = false;
                return;
            }

            // UUID格式验证
            const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
            
            if (!uuidRegex.test(workflowId)) {
                input.className = 'form-input invalid';
                message.textContent = '❌ 工作流ID格式错误（应为UUID格式）';
                message.className = 'validation-message error';
                configValid.workflowId = false;
            } else {
                input.className = 'form-input valid';
                message.textContent = '✅ 工作流ID格式正确';
                message.className = 'validation-message success';
                configValid.workflowId = true;
            }

            updateButtons();
        }

        // 更新按钮状态
        function updateButtons() {
            const allValid = configValid.apiKey && configValid.workflowId;
            document.getElementById('goToChatBtn').style.display = allValid ? 'inline-block' : 'none';
        }

        // 测试工作流
        async function testWorkflow() {
            const apiKey = document.getElementById('apiKey').value;
            const workflowId = document.getElementById('workflowId').value;
            const resultDiv = document.getElementById('testResult');

            if (!configValid.apiKey || !configValid.workflowId) {
                resultDiv.innerHTML = '<div class="alert alert-warning">请先正确配置API密钥和工作流ID</div>';
                return;
            }

            resultDiv.innerHTML = '<div class="alert alert-info">🔄 正在测试工作流连接...</div>';

            try {
                const response = await fetch('https://cloud.dify.ai/v1/workflows/run', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        inputs: {
                            query: '测试生态拓展工作流连接'
                        },
                        response_mode: 'blocking',
                        user: 'test_user'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = '<div class="alert alert-success">✅ 工作流测试成功！可以正常使用。</div>';
                } else {
                    const errorText = await response.text();
                    resultDiv.innerHTML = `<div class="alert alert-warning">⚠️ 工作流测试失败：${response.status} - ${errorText}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="alert alert-warning">❌ 网络错误：${error.message}</div>`;
            }
        }

        // 生成配置
        function generateConfig() {
            const apiKey = document.getElementById('apiKey').value;
            const workflowId = document.getElementById('workflowId').value;

            if (!configValid.apiKey || !configValid.workflowId) {
                alert('请先正确配置API密钥和工作流ID');
                return;
            }

            const configContent = `// 生态拓展工作流配置
ecology: {
    name: '生态拓展专家',
    type: 'workflow',
    apiKey: '${apiKey}',
    apiUrl: 'https://cloud.dify.ai/v1/workflows/run',
    workflowId: '${workflowId}',
    description: '智能分析市场生态，构建合作伙伴网络，扩大业务生态圈',
    features: ['AI分析', '合作伙伴', '生态布局'],
    knowledgeBase: '生态拓展知识库',
    fileTypes: ['.pdf', '.doc', '.docx', '.txt', '.md'],
    maxFileSize: 10 * 1024 * 1024,
    workflowInputs: {
        query: 'query',
        context: 'context',
        user_info: 'user_info'
    },
    workflowOutputs: {
        result: 'result',
        analysis: 'analysis',
        recommendations: 'recommendations'
    },
    welcomeMessage: \`您好！我是生态拓展专家，基于Dify智能工作流为您提供专业的生态分析服务。

🔄 工作流功能：
• 智能分析市场生态格局
• 识别潜在合作伙伴
• 制定生态拓展策略
• 评估生态合作风险
• 生成生态合作建议

请上传相关文档或直接提问，我将通过专业的AI工作流为您提供深度分析！\`
}`;

            // 下载配置文件
            const blob = new Blob([configContent], { type: 'text/javascript' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'ecology-workflow-config.js';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            alert('配置已生成！请将内容复制到 services-config.js 文件中的 ecology 部分。');
        }

        // 跳转到生态拓展聊天页面
        function goToEcologyChat() {
            window.location.href = 'chat-ecology.html';
        }
    </script>
</body>
</html>
