/**
 * 简化的Dify工作流测试
 * 直接使用fetch测试API连接
 */

async function testDifyAPI() {
    console.log('🔍 简化Dify API测试\n');
    
    // 从截图中的配置
    const apiKey = 'app-ZRuKpcHUrE5E6zTNQLddDCc';
    const workflowId = '8cc0dc2f-37ed-4dbd-b647-0fc278810788';
    const apiUrl = 'https://api.dify.ai/v1/workflows/run';
    
    console.log('📋 测试配置:');
    console.log(`API URL: ${apiUrl}`);
    console.log(`工作流ID: ${workflowId}`);
    console.log(`API密钥: ${apiKey.substring(0, 10)}...`);
    console.log('');
    
    // 构建请求
    const requestBody = {
        inputs: {
            query: "测试查询"
        },
        response_mode: "blocking",
        user: "test_user_" + Date.now()
    };
    
    console.log('📤 发送请求:', JSON.stringify(requestBody, null, 2));
    console.log('');
    
    try {
        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${apiKey}`,
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify(requestBody)
        });
        
        console.log(`📡 响应状态: ${response.status} ${response.statusText}`);
        
        const responseText = await response.text();
        console.log('📥 响应内容:', responseText);
        
        if (response.ok) {
            console.log('\n✅ API调用成功!');
            try {
                const jsonResponse = JSON.parse(responseText);
                console.log('📊 解析后的响应:', JSON.stringify(jsonResponse, null, 2));
            } catch (e) {
                console.log('⚠️ 响应不是有效的JSON格式');
            }
        } else {
            console.log('\n❌ API调用失败');
            
            // 尝试解析错误信息
            try {
                const errorData = JSON.parse(responseText);
                console.log('错误详情:', errorData);
                
                if (response.status === 401) {
                    console.log('\n🔑 认证问题:');
                    console.log('- API密钥可能无效或已过期');
                    console.log('- 请检查Dify控制台中的API密钥');
                    console.log('- 确保工作流已发布并且API密钥有权限访问');
                }
                
                if (response.status === 404) {
                    console.log('\n🔍 工作流问题:');
                    console.log('- 工作流ID可能错误');
                    console.log('- 工作流可能未发布');
                    console.log('- 检查工作流是否存在于当前应用中');
                }
                
            } catch (e) {
                console.log('无法解析错误响应');
            }
        }
        
    } catch (error) {
        console.error('❌ 请求异常:', error.message);
        
        if (error.message.includes('fetch failed')) {
            console.log('\n🌐 网络问题:');
            console.log('- 检查网络连接');
            console.log('- 确认API端点是否正确');
            console.log('- 可能需要配置代理');
        }
    }
}

// 运行测试
if (require.main === module) {
    testDifyAPI().catch(console.error);
}

module.exports = { testDifyAPI };
