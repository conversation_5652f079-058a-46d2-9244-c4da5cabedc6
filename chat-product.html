<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品研发专家 - AGI2B</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .chat-container {
            width: 90%;
            max-width: 900px;
            height: 90vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            color: #333;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .service-icon {
            font-size: 2rem;
        }

        .service-info h1 {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .service-info p {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .header-actions {
            display: flex;
            gap: 10px;
        }

        .action-button {
            background: rgba(255, 255, 255, 0.3);
            color: #333;
            border: none;
            padding: 8px 15px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background 0.3s;
        }

        .action-button:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
            line-height: 1.5;
        }

        .message.user .message-content {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            color: #333;
            border-bottom-right-radius: 4px;
        }

        .message.assistant .message-content {
            background: white;
            color: #333;
            border: 1px solid #e0e0e0;
            border-bottom-left-radius: 4px;
        }

        .chat-input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #e0e0e0;
        }

        .input-group {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .chat-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            outline: none;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .chat-input:focus {
            border-color: #fcb69f;
        }

        .send-button {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            color: #333;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: opacity 0.3s;
        }

        .send-button:hover {
            opacity: 0.9;
        }

        .send-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .file-upload-area {
            margin-bottom: 15px;
            padding: 15px;
            border: 2px dashed #e0e0e0;
            border-radius: 10px;
            text-align: center;
            cursor: pointer;
            transition: border-color 0.3s;
        }

        .file-upload-area:hover {
            border-color: #fcb69f;
        }

        .file-upload-area.dragover {
            border-color: #fcb69f;
            background: rgba(252, 182, 159, 0.1);
        }

        .uploaded-files {
            margin-top: 10px;
        }

        .file-item {
            display: inline-block;
            background: #f0f0f0;
            padding: 5px 10px;
            border-radius: 15px;
            margin: 5px;
            font-size: 0.9rem;
        }

        .typing-indicator {
            display: none;
            padding: 10px 16px;
            background: white;
            border-radius: 18px;
            border: 1px solid #e0e0e0;
            max-width: 70px;
            margin-bottom: 15px;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dots span {
            width: 8px;
            height: 8px;
            background: #999;
            border-radius: 50%;
            animation: typing 1.4s infinite;
        }

        .typing-dots span:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-dots span:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typing {
            0%, 60%, 100% {
                transform: translateY(0);
            }
            30% {
                transform: translateY(-10px);
            }
        }

        .error-message {
            background: #ffebee;
            color: #c62828;
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #c62828;
        }

        .knowledge-status {
            background: rgba(252, 182, 159, 0.1);
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 15px;
            font-size: 0.9rem;
            text-align: center;
        }

        .product-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-bottom: 15px;
        }

        .feature-card {
            background: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            border: 1px solid #e0e0e0;
        }

        .feature-icon {
            font-size: 1.5rem;
            margin-bottom: 8px;
        }

        .feature-title {
            font-size: 0.9rem;
            font-weight: 600;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <div class="header-left">
                <div class="service-icon">🚀</div>
                <div class="service-info">
                    <h1>产品研发专家</h1>
                    <p>市场需求分析，产品定制，提升竞争力</p>
                </div>
            </div>
            <div class="header-actions">
                <button class="action-button" onclick="goBack()">← 返回</button>
                <button class="action-button" onclick="clearChat()">清空对话</button>
            </div>
        </div>
        
        <div class="chat-messages" id="chatMessages">
            <div class="knowledge-status">
                📚 产品开发知识库已加载 | 🤖 RAG智能检索已启用
            </div>
            
            <div class="product-features">
                <div class="feature-card">
                    <div class="feature-icon">📊</div>
                    <div class="feature-title">需求分析</div>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🎯</div>
                    <div class="feature-title">产品定位</div>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">⚡</div>
                    <div class="feature-title">功能设计</div>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🔄</div>
                    <div class="feature-title">迭代优化</div>
                </div>
            </div>
            
            <div class="message assistant">
                <div class="message-content">
                    您好！我是产品研发专家，专注于帮助企业进行市场需求分析、产品设计优化、技术方案制定。
                    
                    我可以帮助您：
                    • 分析市场需求和用户痛点
                    • 制定产品功能规划和路线图
                    • 优化产品设计和用户体验
                    • 评估技术可行性和成本
                    • 制定产品迭代策略
                    • 竞品分析和差异化定位
                    
                    请上传产品文档或直接提问，我将基于专业产品知识库为您提供精准建议！
                </div>
            </div>
        </div>

        <div class="typing-indicator" id="typingIndicator">
            <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>

        <div class="chat-input-container">
            <div class="file-upload-area" id="fileUploadArea">
                🚀 点击或拖拽上传产品文档到知识库 (支持PRD、原型图、需求文档等)
                <input type="file" id="fileInput" style="display: none;" multiple accept=".pdf,.doc,.docx,.txt,.md,.ppt,.pptx,.jpg,.png">
                <div class="uploaded-files" id="uploadedFiles"></div>
            </div>
            
            <div class="input-group">
                <input 
                    type="text" 
                    class="chat-input" 
                    id="chatInput" 
                    placeholder="请输入您的产品研发问题..."
                    maxlength="1000"
                >
                <button class="send-button" id="sendButton">发送</button>
            </div>
        </div>
    </div>

    <script>
        // 服务配置
        const SERVICE_CONFIG = {
            name: '产品研发专家',
            type: 'product',
            apiKey: 'app-your-product-api-key', // 需要替换为实际的API密钥
            apiUrl: 'https://cloud.dify.ai/v1/chat-messages',
            welcomeMessage: '您好！我是产品研发专家，专注于帮助企业进行市场需求分析、产品设计优化、技术方案制定。',
            placeholder: '请输入您的产品研发问题...'
        };

        // 初始化聊天功能
        function initChat() {
            const chatInput = document.getElementById('chatInput');
            const sendButton = document.getElementById('sendButton');
            
            sendButton.addEventListener('click', sendMessage);
            chatInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });
        }

        // 发送消息
        function sendMessage() {
            const chatInput = document.getElementById('chatInput');
            const message = chatInput.value.trim();

            if (!message) return;

            addMessage(message, 'user');
            chatInput.value = '';

            // 显示打字指示器
            showTypingIndicator();

            // 模拟API调用（实际使用时需要连接到Dify API）
            setTimeout(() => {
                hideTypingIndicator();
                const response = generateProductResponse(message);
                addMessage(response, 'assistant');
            }, 1500);
        }

        // 生成产品研发相关回复（示例）
        function generateProductResponse(message) {
            const lowerMessage = message.toLowerCase();

            if (lowerMessage.includes('需求') || lowerMessage.includes('用户')) {
                return "需求分析建议：\n1. 通过用户调研和数据分析识别真实需求\n2. 建立用户画像和使用场景模型\n3. 优先级排序，聚焦核心需求\n4. 建立需求验证和反馈机制\n\n您希望深入了解哪个环节的需求分析方法？";
            } else if (lowerMessage.includes('功能') || lowerMessage.includes('设计')) {
                return "产品功能设计建议：\n1. 基于用户旅程设计功能流程\n2. 遵循简洁易用的设计原则\n3. 考虑技术可行性和开发成本\n4. 建立功能测试和优化机制\n\n需要我提供具体的功能设计框架或原型建议吗？";
            } else if (lowerMessage.includes('竞品') || lowerMessage.includes('市场')) {
                return "市场竞品分析建议：\n1. 识别直接和间接竞争对手\n2. 分析竞品的功能特点和优劣势\n3. 找出市场空白和差异化机会\n4. 制定产品定位和竞争策略\n\n您想了解哪个行业或产品类型的竞品分析方法？";
            } else if (lowerMessage.includes('技术') || lowerMessage.includes('开发')) {
                return "技术方案建议：\n1. 评估技术可行性和实现难度\n2. 选择合适的技术架构和工具\n3. 考虑性能、安全性和扩展性\n4. 制定开发计划和里程碑\n\n需要我分析具体的技术选型或架构设计吗？";
            } else {
                const responses = [
                    "基于产品分析，我建议您重点关注：1) 用户需求验证 2) 功能优先级排序 3) 技术可行性评估。需要我详细分析哪个方面？",
                    "从产品角度看，您的问题涉及到产品生命周期管理。建议建立完整的产品规划流程，包括需求分析、设计开发、测试优化等环节。",
                    "产品研发需要考虑：用户体验、技术实现、商业价值、市场竞争等多个维度。我可以帮您制定具体的产品策略和开发计划。"
                ];
                return responses[Math.floor(Math.random() * responses.length)];
            }
        }

        // 添加消息到聊天界面
        function addMessage(content, type) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;

            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            contentDiv.textContent = content;

            messageDiv.appendChild(contentDiv);
            chatMessages.appendChild(messageDiv);

            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // 显示/隐藏打字指示器
        function showTypingIndicator() {
            document.getElementById('typingIndicator').style.display = 'block';
        }

        function hideTypingIndicator() {
            document.getElementById('typingIndicator').style.display = 'none';
        }

        // 文件上传功能
        function initFileUpload() {
            const fileUploadArea = document.getElementById('fileUploadArea');
            const fileInput = document.getElementById('fileInput');
            const uploadedFiles = document.getElementById('uploadedFiles');

            fileUploadArea.addEventListener('click', () => fileInput.click());

            fileInput.addEventListener('change', handleFileUpload);

            // 拖拽上传
            fileUploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                fileUploadArea.classList.add('dragover');
            });

            fileUploadArea.addEventListener('dragleave', () => {
                fileUploadArea.classList.remove('dragover');
            });

            fileUploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                fileUploadArea.classList.remove('dragover');
                handleFileUpload({ target: { files: e.dataTransfer.files } });
            });
        }

        // 处理文件上传
        function handleFileUpload(event) {
            const files = event.target.files;
            const uploadedFiles = document.getElementById('uploadedFiles');

            for (let file of files) {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.textContent = `🚀 ${file.name}`;
                uploadedFiles.appendChild(fileItem);

                // 这里应该调用实际的文件上传API
                console.log('上传产品文档到知识库:', file.name);
            }
        }

        // 返回服务列表
        function goBack() {
            window.location.href = 'services.html';
        }

        // 清空对话
        function clearChat() {
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.innerHTML = `
                <div class="knowledge-status">
                    📚 产品开发知识库已加载 | 🤖 RAG智能检索已启用
                </div>

                <div class="product-features">
                    <div class="feature-card">
                        <div class="feature-icon">📊</div>
                        <div class="feature-title">需求分析</div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🎯</div>
                        <div class="feature-title">产品定位</div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">⚡</div>
                        <div class="feature-title">功能设计</div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🔄</div>
                        <div class="feature-title">迭代优化</div>
                    </div>
                </div>

                <div class="message assistant">
                    <div class="message-content">
                        您好！我是产品研发专家，专注于帮助企业进行市场需求分析、产品设计优化、技术方案制定。

                        我可以帮助您：
                        • 分析市场需求和用户痛点
                        • 制定产品功能规划和路线图
                        • 优化产品设计和用户体验
                        • 评估技术可行性和成本
                        • 制定产品迭代策略
                        • 竞品分析和差异化定位

                        请上传产品文档或直接提问，我将基于专业产品知识库为您提供精准建议！
                    </div>
                </div>
            `;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            initChat();
            initFileUpload();
        });
    </script>
</body>
</html>
