<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AGI2B 服务配置向导</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .content {
            padding: 30px;
        }

        .step {
            margin-bottom: 40px;
        }

        .step-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .step-number {
            background: #667eea;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9rem;
            font-weight: bold;
        }

        .step-content {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .service-config {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .service-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }

        .service-icon {
            font-size: 2rem;
        }

        .service-info h3 {
            font-size: 1.2rem;
            color: #333;
            margin-bottom: 5px;
        }

        .service-info p {
            color: #666;
            font-size: 0.9rem;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-label {
            display: block;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .form-input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-input:focus {
            outline: none;
            border-color: #667eea;
        }

        .form-input.valid {
            border-color: #28a745;
        }

        .form-input.invalid {
            border-color: #dc3545;
        }

        .validation-message {
            font-size: 0.8rem;
            margin-top: 5px;
        }

        .validation-message.success {
            color: #28a745;
        }

        .validation-message.error {
            color: #dc3545;
        }

        .button-group {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }

        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #218838;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 10px;
        }

        .status-indicator.pending {
            background: #ffc107;
        }

        .status-indicator.valid {
            background: #28a745;
        }

        .status-indicator.invalid {
            background: #dc3545;
        }

        .help-text {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }

        .help-text h4 {
            color: #1976d2;
            margin-bottom: 10px;
        }

        .help-text ol {
            margin-left: 20px;
        }

        .help-text li {
            margin-bottom: 5px;
            color: #333;
        }

        .progress-bar {
            background: #e0e0e0;
            height: 8px;
            border-radius: 4px;
            margin-bottom: 20px;
            overflow: hidden;
        }

        .progress-fill {
            background: linear-gradient(90deg, #667eea, #764ba2);
            height: 100%;
            transition: width 0.3s ease;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
            }
            
            .content {
                padding: 20px;
            }
            
            .button-group {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 AGI2B 服务配置向导</h1>
            <p>配置您的AI服务，开启智能化业务之旅</p>
        </div>

        <div class="content">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill" style="width: 0%"></div>
            </div>

            <div class="step">
                <div class="step-title">
                    <div class="step-number">1</div>
                    获取Dify API密钥
                </div>
                <div class="step-content">
                    <div class="help-text">
                        <h4>📋 如何获取API密钥：</h4>
                        <ol>
                            <li>访问 <a href="https://cloud.dify.ai" target="_blank">Dify控制台</a></li>
                            <li>登录您的账户</li>
                            <li>为每个服务创建对应的应用</li>
                            <li>进入应用的"API访问"页面</li>
                            <li>复制API密钥（格式：app-xxxxxxxxxx）</li>
                        </ol>
                    </div>
                </div>
            </div>

            <div class="step">
                <div class="step-title">
                    <div class="step-number">2</div>
                    配置服务API密钥
                </div>
                <div class="step-content">
                    <!-- 生态拓展服务 -->
                    <div class="service-config">
                        <div class="service-header">
                            <div class="service-icon">🌱</div>
                            <div class="service-info">
                                <h3>生态拓展专家 <span class="status-indicator pending" id="ecology-status"></span></h3>
                                <p>智能分析市场生态，构建合作伙伴网络</p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">API密钥：</label>
                            <input type="text" class="form-input" id="ecology-api-key" 
                                   placeholder="app-xxxxxxxxxx" 
                                   onchange="validateApiKey('ecology', this.value)">
                            <div class="validation-message" id="ecology-message"></div>
                        </div>
                    </div>

                    <!-- 销售助理服务 -->
                    <div class="service-config">
                        <div class="service-header">
                            <div class="service-icon">🤝</div>
                            <div class="service-info">
                                <h3>销售助理专家 <span class="status-indicator pending" id="sales-status"></span></h3>
                                <p>AI驱动的销售流程优化，提升转化率</p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">API密钥：</label>
                            <input type="text" class="form-input" id="sales-api-key" 
                                   placeholder="app-xxxxxxxxxx" 
                                   onchange="validateApiKey('sales', this.value)">
                            <div class="validation-message" id="sales-message"></div>
                        </div>
                    </div>

                    <!-- 产品研发服务 -->
                    <div class="service-config">
                        <div class="service-header">
                            <div class="service-icon">🚀</div>
                            <div class="service-info">
                                <h3>产品研发专家 <span class="status-indicator pending" id="product-status"></span></h3>
                                <p>市场需求分析，产品定制，提升竞争力</p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">API密钥：</label>
                            <input type="text" class="form-input" id="product-api-key" 
                                   placeholder="app-xxxxxxxxxx" 
                                   onchange="validateApiKey('product', this.value)">
                            <div class="validation-message" id="product-message"></div>
                        </div>
                    </div>

                    <!-- 售前支持服务 -->
                    <div class="service-config">
                        <div class="service-header">
                            <div class="service-icon">💬</div>
                            <div class="service-info">
                                <h3>售前支持专家 <span class="status-indicator pending" id="support-status"></span></h3>
                                <p>智能客户需求分析，方案定制，提升成单率</p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">API密钥：</label>
                            <input type="text" class="form-input" id="support-api-key" 
                                   placeholder="app-xxxxxxxxxx" 
                                   onchange="validateApiKey('support', this.value)">
                            <div class="validation-message" id="support-message"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="button-group">
                <button class="btn btn-secondary" onclick="testAllServices()">🧪 测试所有服务</button>
                <button class="btn btn-primary" onclick="saveConfiguration()">💾 保存配置</button>
                <button class="btn btn-success" onclick="goToServices()" id="goToServicesBtn" style="display: none;">🚀 开始使用</button>
            </div>
        </div>
    </div>

    <script>
        // 配置状态
        let configStatus = {
            ecology: false,
            sales: false,
            product: false,
            support: false
        };

        // 验证API密钥格式
        function validateApiKey(serviceType, apiKey) {
            const input = document.getElementById(`${serviceType}-api-key`);
            const message = document.getElementById(`${serviceType}-message`);
            const status = document.getElementById(`${serviceType}-status`);

            if (!apiKey || apiKey.trim() === '') {
                input.className = 'form-input';
                message.textContent = '';
                status.className = 'status-indicator pending';
                configStatus[serviceType] = false;
                updateProgress();
                return;
            }

            if (!apiKey.startsWith('app-')) {
                input.className = 'form-input invalid';
                message.textContent = '❌ API密钥格式错误（应以 app- 开头）';
                message.className = 'validation-message error';
                status.className = 'status-indicator invalid';
                configStatus[serviceType] = false;
            } else if (apiKey.length < 30) {
                input.className = 'form-input invalid';
                message.textContent = '⚠️ API密钥长度可能不正确';
                message.className = 'validation-message error';
                status.className = 'status-indicator invalid';
                configStatus[serviceType] = false;
            } else {
                input.className = 'form-input valid';
                message.textContent = '✅ API密钥格式正确';
                message.className = 'validation-message success';
                status.className = 'status-indicator valid';
                configStatus[serviceType] = true;
            }

            updateProgress();
        }

        // 更新进度条
        function updateProgress() {
            const validCount = Object.values(configStatus).filter(Boolean).length;
            const progress = (validCount / 4) * 100;
            document.getElementById('progressFill').style.width = `${progress}%`;

            // 显示/隐藏"开始使用"按钮
            if (progress === 100) {
                document.getElementById('goToServicesBtn').style.display = 'inline-block';
            } else {
                document.getElementById('goToServicesBtn').style.display = 'none';
            }
        }

        // 测试所有服务
        async function testAllServices() {
            alert('测试功能开发中，请先保存配置后手动测试各个服务。');
        }

        // 保存配置
        function saveConfiguration() {
            const config = {
                ecology: document.getElementById('ecology-api-key').value,
                sales: document.getElementById('sales-api-key').value,
                product: document.getElementById('product-api-key').value,
                support: document.getElementById('support-api-key').value
            };

            // 检查是否至少配置了一个服务
            const hasValidConfig = Object.values(config).some(key => key && key.startsWith('app-'));
            
            if (!hasValidConfig) {
                alert('请至少配置一个服务的API密钥！');
                return;
            }

            // 生成配置文件内容
            const configContent = generateConfigFile(config);
            
            // 下载配置文件
            downloadConfigFile(configContent);
            
            alert('配置已生成！请将下载的 services-config.js 文件替换项目中的同名文件。');
        }

        // 生成配置文件内容
        function generateConfigFile(config) {
            return `// AGI2B 服务配置文件 - 自动生成于 ${new Date().toLocaleString()}

const SERVICES_CONFIG = {
    ecology: {
        name: '生态拓展专家',
        apiKey: '${config.ecology || 'app-your-ecology-api-key-here'}',
        apiUrl: 'https://cloud.dify.ai/v1/chat-messages',
        // ... 其他配置保持不变
    },
    sales: {
        name: '销售助理专家', 
        apiKey: '${config.sales || 'app-your-sales-api-key-here'}',
        apiUrl: 'https://cloud.dify.ai/v1/chat-messages',
        // ... 其他配置保持不变
    },
    product: {
        name: '产品研发专家',
        apiKey: '${config.product || 'app-your-product-api-key-here'}',
        apiUrl: 'https://cloud.dify.ai/v1/chat-messages',
        // ... 其他配置保持不变
    },
    support: {
        name: '售前支持专家',
        apiKey: '${config.support || 'app-your-support-api-key-here'}',
        apiUrl: 'https://cloud.dify.ai/v1/chat-messages',
        // ... 其他配置保持不变
    }
};

// 请将此文件的完整内容复制到您的 services-config.js 文件中`;
        }

        // 下载配置文件
        function downloadConfigFile(content) {
            const blob = new Blob([content], { type: 'text/javascript' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'services-config.js';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // 跳转到服务页面
        function goToServices() {
            window.location.href = 'services.html';
        }
    </script>
</body>
</html>
