/**
 * 测试不同的Dify API端点
 */

const https = require('https');
const { URL } = require('url');

const API_KEY = 'app-ZRuKpcHUrE5E6zTNQLddDCc';
const WORKFLOW_ID = '8cc0dc2f-37ed-4dbd-b647-0fc278810788';

// 可能的API端点
const ENDPOINTS = [
    'https://api.dify.ai/v1/workflows/run',
    'https://cloud.dify.ai/v1/workflows/run',
    `https://api.dify.ai/v1/workflows/${WORKFLOW_ID}/run`,
    `https://cloud.dify.ai/v1/workflows/${WORKFLOW_ID}/run`
];

function makeRequest(url, apiKey) {
    return new Promise((resolve) => {
        const parsedUrl = new URL(url);
        
        const requestBody = JSON.stringify({
            inputs: { query: "test" },
            response_mode: "blocking",
            user: "test_user"
        });
        
        const options = {
            hostname: parsedUrl.hostname,
            port: 443,
            path: parsedUrl.pathname,
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${apiKey}`,
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            timeout: 10000
        };
        
        const req = https.request(options, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                resolve({
                    url,
                    status: res.statusCode,
                    statusText: res.statusMessage,
                    response: data,
                    success: res.statusCode === 200
                });
            });
        });
        
        req.on('error', (error) => {
            resolve({
                url,
                status: 'ERROR',
                error: error.message,
                success: false
            });
        });
        
        req.on('timeout', () => {
            req.destroy();
            resolve({
                url,
                status: 'TIMEOUT',
                error: 'Request timeout',
                success: false
            });
        });
        
        req.write(requestBody);
        req.end();
    });
}

async function testAllEndpoints() {
    console.log('🔍 测试不同的Dify API端点\n');
    console.log(`API密钥: ${API_KEY.substring(0, 10)}...`);
    console.log(`工作流ID: ${WORKFLOW_ID}\n`);
    
    console.log('测试端点:');
    ENDPOINTS.forEach((endpoint, index) => {
        console.log(`${index + 1}. ${endpoint}`);
    });
    console.log('');
    
    const results = [];
    
    for (let i = 0; i < ENDPOINTS.length; i++) {
        const endpoint = ENDPOINTS[i];
        console.log(`📡 测试端点 ${i + 1}: ${endpoint}`);
        
        try {
            const result = await makeRequest(endpoint, API_KEY);
            results.push(result);
            
            if (result.success) {
                console.log(`✅ 成功! 状态: ${result.status}`);
                console.log(`响应: ${result.response.substring(0, 100)}...`);
            } else if (result.status === 401) {
                console.log(`🔑 认证失败 (${result.status})`);
                try {
                    const errorData = JSON.parse(result.response);
                    console.log(`错误: ${errorData.message || '未知错误'}`);
                } catch (e) {
                    console.log(`错误: ${result.response}`);
                }
            } else if (result.status === 404) {
                console.log(`❌ 端点不存在 (${result.status})`);
            } else if (result.status === 'ERROR') {
                console.log(`❌ 连接错误: ${result.error}`);
            } else if (result.status === 'TIMEOUT') {
                console.log(`⏰ 请求超时`);
            } else {
                console.log(`❌ 失败: ${result.status} ${result.statusText}`);
            }
        } catch (error) {
            console.log(`❌ 异常: ${error.message}`);
            results.push({
                url: endpoint,
                status: 'EXCEPTION',
                error: error.message,
                success: false
            });
        }
        
        console.log('');
    }
    
    // 结果汇总
    console.log('📊 测试结果汇总:');
    console.log('='.repeat(60));
    
    const successfulEndpoints = results.filter(r => r.success);
    const authFailures = results.filter(r => r.status === 401);
    const notFoundEndpoints = results.filter(r => r.status === 404);
    const errorEndpoints = results.filter(r => r.status === 'ERROR' || r.status === 'TIMEOUT');
    
    if (successfulEndpoints.length > 0) {
        console.log('\n✅ 成功的端点:');
        successfulEndpoints.forEach(r => {
            console.log(`  - ${r.url}`);
        });
        console.log('\n🎉 找到可用的API端点! 请使用上述端点更新配置。');
    }
    
    if (authFailures.length > 0) {
        console.log('\n🔑 认证失败的端点 (API密钥问题):');
        authFailures.forEach(r => {
            console.log(`  - ${r.url}`);
        });
        console.log('\n💡 这些端点存在但API密钥无效，请检查:');
        console.log('   1. API密钥是否正确');
        console.log('   2. 工作流是否已发布');
        console.log('   3. API密钥是否有权限访问该工作流');
    }
    
    if (notFoundEndpoints.length > 0) {
        console.log('\n❌ 不存在的端点:');
        notFoundEndpoints.forEach(r => {
            console.log(`  - ${r.url}`);
        });
    }
    
    if (errorEndpoints.length > 0) {
        console.log('\n🌐 连接错误的端点:');
        errorEndpoints.forEach(r => {
            console.log(`  - ${r.url} (${r.error})`);
        });
    }
    
    console.log('\n📋 下一步建议:');
    if (successfulEndpoints.length > 0) {
        console.log('1. 使用成功的端点更新 services-config.js');
        console.log('2. 运行 node verify-integration.js 重新测试');
    } else if (authFailures.length > 0) {
        console.log('1. 从Dify控制台获取新的API密钥');
        console.log('2. 确认工作流已发布');
        console.log('3. 运行 node update-api-key.js 更新密钥');
    } else {
        console.log('1. 检查网络连接');
        console.log('2. 确认Dify服务是否可用');
        console.log('3. 联系Dify支持获取正确的API端点');
    }
}

if (require.main === module) {
    testAllEndpoints().catch(console.error);
}

module.exports = { testAllEndpoints };
