<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>获取Dify API密钥详细指南</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #4285f4;
        }
        .step {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #4285f4;
        }
        .step h3 {
            color: #333;
            margin-top: 0;
        }
        .screenshot-placeholder {
            background: #e9ecef;
            border: 2px dashed #adb5bd;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            color: #6c757d;
            margin: 15px 0;
        }
        .code {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 14px;
            margin: 10px 0;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            color: #856404;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            color: #155724;
        }
        .btn {
            display: inline-block;
            background: #4285f4;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 5px;
        }
        .btn:hover {
            background: #3367d6;
        }
        ol {
            padding-left: 20px;
        }
        li {
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔑 获取Dify API密钥详细指南</h1>
            <p>解决"Access Token is invalid"错误</p>
        </div>

        <div class="warning">
            <strong>⚠️ 重要提示</strong><br>
            从您的错误信息来看，当前使用的API密钥无效。您需要获取正确的API密钥才能使用应用。
        </div>

        <div class="step">
            <h3>📋 步骤1: 访问Dify控制台</h3>
            <ol>
                <li>打开浏览器，访问 <a href="https://cloud.dify.ai" target="_blank">https://cloud.dify.ai</a></li>
                <li>使用您的账户登录</li>
                <li>如果没有账户，请先注册一个</li>
            </ol>
            <div class="screenshot-placeholder">
                [这里应该显示Dify登录页面的截图]
            </div>
        </div>

        <div class="step">
            <h3>🎯 步骤2: 选择或创建应用</h3>
            <ol>
                <li>登录后，您会看到应用列表</li>
                <li>如果您已有应用，点击进入</li>
                <li>如果没有应用，点击"创建应用"按钮</li>
                <li>选择"聊天助手"类型的应用</li>
            </ol>
            <div class="screenshot-placeholder">
                [这里应该显示应用列表页面的截图]
            </div>
        </div>

        <div class="step">
            <h3>🔧 步骤3: 进入API访问页面</h3>
            <ol>
                <li>在应用页面，查看左侧菜单</li>
                <li>找到并点击"API访问"或"API Access"</li>
                <li>这里会显示API相关的配置信息</li>
            </ol>
            <div class="screenshot-placeholder">
                [这里应该显示API访问菜单的截图]
            </div>
        </div>

        <div class="step">
            <h3>🔑 步骤4: 获取API密钥</h3>
            <ol>
                <li>在API访问页面，找到"API密钥"部分</li>
                <li>如果没有密钥，点击"创建密钥"按钮</li>
                <li>复制生成的API密钥</li>
                <li>API密钥格式应该类似：<code>app-abc123def456ghi789</code></li>
            </ol>
            <div class="code">
                正确的API密钥格式示例：<br>
                app-1234567890abcdef1234567890abcdef
            </div>
            <div class="warning">
                <strong>注意</strong>：API密钥通常很长（30-40个字符），以"app-"开头。如果您的密钥不是这种格式，可能不是正确的API密钥。
            </div>
        </div>

        <div class="step">
            <h3>⚙️ 步骤5: 配置应用</h3>
            <ol>
                <li>复制API密钥后，返回到您的EcoBilder应用</li>
                <li>打开 <code>config.js</code> 文件</li>
                <li>找到这一行：<code>API_KEY: 'YOUR_API_KEY_HERE',</code></li>
                <li>将 <code>YOUR_API_KEY_HERE</code> 替换为您刚才复制的API密钥</li>
                <li>保存文件</li>
            </ol>
            <div class="code">
                修改前：<br>
                API_KEY: 'YOUR_API_KEY_HERE',<br><br>
                修改后：<br>
                API_KEY: 'app-1234567890abcdef1234567890abcdef',
            </div>
        </div>

        <div class="step">
            <h3>✅ 步骤6: 测试连接</h3>
            <ol>
                <li>保存配置文件后，刷新浏览器页面</li>
                <li>或者使用调试工具重新测试API连接</li>
                <li>如果配置正确，应该能够正常聊天</li>
            </ol>
        </div>

        <div class="success">
            <strong>✅ 成功标志</strong><br>
            如果配置正确，您应该能够：
            <ul>
                <li>在聊天界面发送消息</li>
                <li>收到AI的回复</li>
                <li>不再看到"网络连接错误"的提示</li>
            </ul>
        </div>

        <div class="step">
            <h3>🛠️ 常见问题排查</h3>
            <p><strong>如果仍然出现错误，请检查：</strong></p>
            <ul>
                <li>API密钥是否以"app-"开头</li>
                <li>API密钥是否完整复制（没有多余的空格或换行）</li>
                <li>config.js文件是否正确保存</li>
                <li>浏览器是否已刷新页面</li>
                <li>Dify应用是否已发布并可用</li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <a href="debug.html" class="btn">🔧 返回调试工具</a>
            <a href="index.html" class="btn">💬 返回聊天界面</a>
        </div>
    </div>
</body>
</html>
