<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI生态专家 - Dify Chat</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .chat-container {
            width: 90%;
            max-width: 800px;
            height: 90vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: #4285f4;
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 18px;
            font-weight: 600;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
        }

        .message.user .message-content {
            background: #4285f4;
            color: white;
            border-bottom-right-radius: 4px;
        }

        .message.assistant .message-content {
            background: white;
            color: #333;
            border: 1px solid #e0e0e0;
            border-bottom-left-radius: 4px;
        }

        .chat-input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #e0e0e0;
        }

        .input-group {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .chat-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            outline: none;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .chat-input:focus {
            border-color: #4285f4;
        }

        .send-button {
            background: #4285f4;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: background 0.3s;
        }

        .send-button:hover {
            background: #3367d6;
        }

        .send-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 10px;
            color: #666;
        }

        .error-message {
            background: #ffebee;
            color: #c62828;
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #c62828;
        }

        .typing-indicator {
            display: none;
            padding: 10px 16px;
            background: white;
            border-radius: 18px;
            border: 1px solid #e0e0e0;
            max-width: 70px;
            margin-bottom: 15px;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dots span {
            width: 8px;
            height: 8px;
            background: #999;
            border-radius: 50%;
            animation: typing 1.4s infinite;
        }

        .typing-dots span:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-dots span:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typing {
            0%, 60%, 100% {
                transform: translateY(0);
            }
            30% {
                transform: translateY(-10px);
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            🤖 AI生态专家
        </div>
        
        <div class="chat-messages" id="chatMessages">
            <div class="message assistant">
                <div class="message-content">
                    您好！我是AI生态专家，很高兴为您服务。请问有什么可以帮助您的吗？
                </div>
            </div>
        </div>

        <div class="typing-indicator" id="typingIndicator">
            <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>

        <div class="loading" id="loading">正在思考中...</div>

        <div class="chat-input-container">
            <div class="input-group">
                <input 
                    type="text" 
                    class="chat-input" 
                    id="chatInput" 
                    placeholder="请输入您的问题..."
                    maxlength="1000"
                >
                <button class="send-button" id="sendButton">发送</button>
            </div>
        </div>
    </div>

    <!-- 引入配置文件 -->
    <script src="config.js"></script>

    <script>
        class DifyChatClient {
            constructor() {
                this.config = DIFY_CONFIG;
                this.apiUrl = this.config.API_URL;
                this.conversationId = null;
                this.retryCount = 0;

                // DOM元素
                this.chatMessages = document.getElementById('chatMessages');
                this.chatInput = document.getElementById('chatInput');
                this.sendButton = document.getElementById('sendButton');
                this.loading = document.getElementById('loading');
                this.typingIndicator = document.getElementById('typingIndicator');

                this.initEventListeners();
                this.updateUI();
            }

            initEventListeners() {
                this.sendButton.addEventListener('click', () => this.sendMessage());
                this.chatInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.sendMessage();
                    }
                });

                // 设置输入框最大长度
                this.chatInput.maxLength = this.config.APP_CONFIG.MAX_MESSAGE_LENGTH;
            }

            updateUI() {
                // 更新页面标题和欢迎消息
                document.querySelector('.chat-header').textContent = '🤖 ' + this.config.UI_CONFIG.CHAT_TITLE;
                this.chatInput.placeholder = this.config.UI_CONFIG.INPUT_PLACEHOLDER;

                // 更新欢迎消息
                const welcomeMessage = document.querySelector('.message.assistant .message-content');
                if (welcomeMessage) {
                    welcomeMessage.textContent = this.config.UI_CONFIG.WELCOME_MESSAGE;
                }
            }

            async sendMessage() {
                const message = this.chatInput.value.trim();

                // 验证消息
                if (!message) {
                    this.addErrorMessage(this.config.UI_CONFIG.ERROR_MESSAGES.EMPTY_MESSAGE);
                    return;
                }

                if (message.length > this.config.APP_CONFIG.MAX_MESSAGE_LENGTH) {
                    this.addErrorMessage(this.config.UI_CONFIG.ERROR_MESSAGES.MESSAGE_TOO_LONG);
                    return;
                }

                // 添加用户消息到界面
                this.addMessage(message, 'user');
                this.chatInput.value = '';
                this.setLoading(true);
                this.retryCount = 0;

                await this.sendMessageWithRetry(message);
            }

            async sendMessageWithRetry(message) {
                try {
                    // 显示打字指示器
                    this.showTypingIndicator();

                    const response = await this.callDifyAPI(message);

                    // 隐藏打字指示器
                    this.hideTypingIndicator();

                    if (response && response.answer) {
                        this.addMessage(response.answer, 'assistant');
                        if (response.conversation_id) {
                            this.conversationId = response.conversation_id;
                        }
                    } else {
                        throw new Error(this.config.UI_CONFIG.ERROR_MESSAGES.INVALID_RESPONSE);
                    }
                } catch (error) {
                    this.hideTypingIndicator();

                    // 详细错误日志
                    console.error('API调用失败:', error);
                    console.error('错误类型:', error.name);
                    console.error('错误消息:', error.message);
                    console.error('API URL:', this.apiUrl);
                    console.error('API Key:', this.config.API_KEY ? `${this.config.API_KEY.substring(0, 10)}...` : 'undefined');

                    // 重试逻辑
                    if (this.retryCount < this.config.APP_CONFIG.MAX_RETRIES) {
                        this.retryCount++;
                        console.log(`重试第 ${this.retryCount} 次...`);
                        setTimeout(() => this.sendMessageWithRetry(message), 1000 * this.retryCount);
                        return;
                    }

                    // 显示错误消息
                    let errorMessage = this.config.UI_CONFIG.ERROR_MESSAGES.API_ERROR;
                    if (error.name === 'TypeError' && error.message.includes('fetch')) {
                        errorMessage = this.config.UI_CONFIG.ERROR_MESSAGES.NETWORK_ERROR;
                    } else if (error.message.includes('timeout')) {
                        errorMessage = this.config.UI_CONFIG.ERROR_MESSAGES.TIMEOUT_ERROR;
                    }

                    // 在错误消息中包含具体错误信息
                    errorMessage += ` (${error.message})`;
                    this.addErrorMessage(errorMessage);
                    console.error('API调用错误:', error);
                } finally {
                    this.setLoading(false);
                }
            }

            async callDifyAPI(message) {
                const requestBody = {
                    inputs: {},
                    query: message,
                    response_mode: this.config.APP_CONFIG.RESPONSE_MODE,
                    conversation_id: this.conversationId || '',
                    user: this.config.APP_CONFIG.USER_PREFIX + Date.now(),
                    auto_generate_name: false,
                    files: []
                };

                console.log('发送API请求:', JSON.stringify(requestBody, null, 2));

                // 创建带超时的fetch请求
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), this.config.APP_CONFIG.REQUEST_TIMEOUT);

                try {
                    const response = await fetch(this.apiUrl, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${this.config.API_KEY}`,
                            'Accept': 'application/json',
                            'User-Agent': 'EcoBilder/1.0'
                        },
                        body: JSON.stringify(requestBody),
                        signal: controller.signal
                    });

                    clearTimeout(timeoutId);

                    if (!response.ok) {
                        const errorText = await response.text();
                        console.error('API响应错误:', response.status, response.statusText);
                        console.error('错误详情:', errorText);
                        throw new Error(`HTTP错误: ${response.status} - ${response.statusText}. 详情: ${errorText}`);
                    }

                    const responseData = await response.json();
                    console.log('API响应成功:', responseData);
                    return responseData;
                } catch (error) {
                    clearTimeout(timeoutId);
                    if (error.name === 'AbortError') {
                        throw new Error('请求超时');
                    }
                    console.error('API请求异常:', error);
                    throw error;
                }
            }

            addMessage(content, type) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${type}`;
                
                const contentDiv = document.createElement('div');
                contentDiv.className = 'message-content';
                contentDiv.textContent = content;
                
                messageDiv.appendChild(contentDiv);
                this.chatMessages.appendChild(messageDiv);
                
                this.scrollToBottom();
            }

            addErrorMessage(content) {
                const errorDiv = document.createElement('div');
                errorDiv.className = 'error-message';
                errorDiv.textContent = content;
                this.chatMessages.appendChild(errorDiv);
                this.scrollToBottom();
            }

            showTypingIndicator() {
                this.typingIndicator.style.display = 'block';
                this.scrollToBottom();
            }

            hideTypingIndicator() {
                this.typingIndicator.style.display = 'none';
            }

            setLoading(isLoading) {
                this.sendButton.disabled = isLoading;
                this.chatInput.disabled = isLoading;
                this.loading.style.display = isLoading ? 'block' : 'none';
            }

            scrollToBottom() {
                setTimeout(() => {
                    this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
                }, 100);
            }
        }

        // 初始化聊天客户端
        document.addEventListener('DOMContentLoaded', () => {
            // 检查配置
            if (!validateConfig()) {
                // 如果配置无效，显示错误信息和调试链接
                const chatMessages = document.getElementById('chatMessages');
                chatMessages.innerHTML = `
                    <div class="error-message">
                        <strong>⚠️ 配置错误</strong><br>
                        API密钥未正确配置。请按以下步骤操作：<br><br>
                        1. 登录您的 <a href="https://cloud.dify.ai" target="_blank">Dify控制台</a><br>
                        2. 选择您的应用<br>
                        3. 进入"API访问"页面<br>
                        4. 复制API密钥（格式：app-xxxxxxxxxx）<br>
                        5. 在config.js文件中替换 YOUR_API_KEY_HERE<br><br>
                        <a href="debug.html" target="_blank" style="color: #1976d2;">🔧 打开调试工具</a>
                    </div>
                `;
                return;
            }

            new DifyChatClient();
        });
    </script>
</body>
</html>
