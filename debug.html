<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dify API 调试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        button {
            background: #4285f4;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #3367d6;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            color: #2e7d32;
        }
        .error {
            background: #ffebee;
            border: 1px solid #f44336;
            color: #c62828;
        }
        .info {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            color: #1565c0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Dify API 调试工具</h1>
        <p>这个工具可以帮助您测试和调试Dify API连接。</p>
        
        <div class="form-group">
            <label for="apiKey">API 密钥:</label>
            <input type="password" id="apiKey" placeholder="输入您的Dify API密钥 (app-xxxxxxxxxx)">
            <small>格式应该是: app-xxxxxxxxxx</small>
        </div>
        
        <div class="form-group">
            <label for="apiUrl">API 端点:</label>
            <input type="text" id="apiUrl" value="https://api.dify.ai/v1/chat-messages">
        </div>
        
        <div class="form-group">
            <label for="testMessage">测试消息:</label>
            <textarea id="testMessage" rows="3" placeholder="输入要测试的消息">你好，请介绍一下生态建筑的基本概念。</textarea>
        </div>
        
        <button onclick="testAPI()">🚀 测试 API 连接</button>
        <button onclick="testAPIAdvanced()">🔬 高级API测试</button>
        <button onclick="testMCPFormat()">🔧 测试MCP格式</button>
        <button onclick="checkCORS()">🌐 检查 CORS</button>
        <button onclick="validateConfig()">✅ 验证配置</button>
        <button onclick="window.open('api-key-guide.html', '_blank')">📖 获取API密钥指南</button>
        
        <div id="result"></div>
    </div>

    <script>
        function showResult(message, type = 'info') {
            const resultDiv = document.getElementById('result');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
        }

        function validateConfig() {
            const apiKey = document.getElementById('apiKey').value.trim();
            const apiUrl = document.getElementById('apiUrl').value.trim();

            let issues = [];

            if (!apiKey) {
                issues.push('❌ API密钥为空');
                issues.push('💡 请访问 https://cloud.dify.ai 获取API密钥');
            } else if (apiKey === 'YOUR_API_KEY_HERE') {
                issues.push('❌ API密钥未配置，仍为默认值');
                issues.push('💡 请将config.js中的YOUR_API_KEY_HERE替换为真实API密钥');
            } else if (!apiKey.startsWith('app-')) {
                issues.push('❌ API密钥格式错误，应该以 "app-" 开头');
                issues.push('💡 正确格式示例：app-abc123def456');
                issues.push('📖 查看详细指南：api-key-guide.html');
            } else if (apiKey.length < 30) {
                issues.push('❌ API密钥长度可能不正确（应该30+字符）');
                issues.push('💡 请确认复制了完整的API密钥');
            } else {
                issues.push('✅ API密钥格式正确');
            }

            if (!apiUrl) {
                issues.push('❌ API端点为空');
            } else if (!apiUrl.startsWith('https://')) {
                issues.push('❌ API端点应该使用HTTPS');
            } else if (!apiUrl.includes('dify.ai')) {
                issues.push('⚠️ API端点可能不是官方Dify端点');
            } else {
                issues.push('✅ API端点格式正确');
            }

            const result = issues.join('\n');
            const hasErrors = issues.some(issue => issue.includes('❌'));
            showResult(result, hasErrors ? 'error' : 'success');
        }

        async function checkCORS() {
            const apiUrl = document.getElementById('apiUrl').value.trim();
            
            try {
                showResult('正在检查CORS设置...', 'info');
                
                // 尝试发送一个简单的OPTIONS请求
                const response = await fetch(apiUrl, {
                    method: 'OPTIONS',
                    headers: {
                        'Origin': window.location.origin,
                        'Access-Control-Request-Method': 'POST',
                        'Access-Control-Request-Headers': 'Content-Type, Authorization'
                    }
                });
                
                showResult(`CORS检查完成:\n状态码: ${response.status}\n允许的方法: ${response.headers.get('Access-Control-Allow-Methods') || '未设置'}\n允许的头部: ${response.headers.get('Access-Control-Allow-Headers') || '未设置'}`, 'success');
            } catch (error) {
                showResult(`CORS检查失败:\n${error.message}\n\n这可能表示存在CORS问题。`, 'error');
            }
        }

        async function testAPI() {
            const apiKey = document.getElementById('apiKey').value.trim();
            const apiUrl = document.getElementById('apiUrl').value.trim();
            const message = document.getElementById('testMessage').value.trim();
            
            if (!apiKey || !apiUrl || !message) {
                showResult('请填写所有必需的字段', 'error');
                return;
            }
            
            showResult('正在测试API连接...', 'info');
            
            // 根据Dify API文档，尝试不同的请求格式
            const requestBody = {
                inputs: {},
                query: message,
                response_mode: 'blocking',
                conversation_id: '',
                user: 'debug-user-' + Date.now(),
                auto_generate_name: false,
                files: []
            };

            console.log('发送请求到:', apiUrl);
            console.log('请求体:', JSON.stringify(requestBody, null, 2));
            
            try {
                const startTime = Date.now();
                
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${apiKey}`,
                        'Accept': 'application/json',
                        'User-Agent': 'EcoBilder/1.0'
                    },
                    body: JSON.stringify(requestBody)
                });
                
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                
                const responseText = await response.text();
                let responseData;
                
                try {
                    responseData = JSON.parse(responseText);
                } catch (e) {
                    responseData = responseText;
                }
                
                if (response.ok) {
                    showResult(`✅ API调用成功!\n响应时间: ${responseTime}ms\n状态码: ${response.status}\n\n响应内容:\n${JSON.stringify(responseData, null, 2)}`, 'success');
                } else {
                    showResult(`❌ API调用失败!\n状态码: ${response.status}\n响应时间: ${responseTime}ms\n\n错误详情:\n${JSON.stringify(responseData, null, 2)}`, 'error');
                }
            } catch (error) {
                showResult(`❌ 网络错误:\n${error.message}\n\n可能的原因:\n1. 网络连接问题\n2. CORS策略阻止\n3. API端点不正确\n4. 防火墙阻止`, 'error');
            }
        }
        
        async function testAPIAdvanced() {
            const apiKey = document.getElementById('apiKey').value.trim();
            const apiUrl = document.getElementById('apiUrl').value.trim();
            const message = document.getElementById('testMessage').value.trim() || '你好';

            if (!apiKey || !apiUrl) {
                showResult('请填写API密钥和端点', 'error');
                return;
            }

            showResult('正在进行高级API测试...', 'info');

            // 测试多种不同的请求格式
            const testFormats = [
                {
                    name: '标准格式',
                    body: {
                        inputs: {},
                        query: message,
                        response_mode: 'blocking',
                        conversation_id: '',
                        user: 'test-user'
                    }
                },
                {
                    name: '简化格式',
                    body: {
                        query: message,
                        user: 'test-user'
                    }
                },
                {
                    name: '完整格式',
                    body: {
                        inputs: {},
                        query: message,
                        response_mode: 'blocking',
                        conversation_id: '',
                        user: 'test-user',
                        auto_generate_name: false,
                        files: []
                    }
                }
            ];

            let results = [];

            for (let format of testFormats) {
                try {
                    console.log(`测试${format.name}:`, format.body);

                    const response = await fetch(apiUrl, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${apiKey}`,
                            'Accept': 'application/json'
                        },
                        body: JSON.stringify(format.body)
                    });

                    const responseText = await response.text();
                    let responseData;

                    try {
                        responseData = JSON.parse(responseText);
                    } catch (e) {
                        responseData = responseText;
                    }

                    results.push(`${format.name}: ${response.status} - ${response.ok ? '✅ 成功' : '❌ 失败'}`);

                    if (response.ok) {
                        results.push(`响应: ${JSON.stringify(responseData, null, 2)}`);
                        break; // 找到成功的格式就停止
                    } else {
                        results.push(`错误: ${JSON.stringify(responseData, null, 2)}`);
                    }

                } catch (error) {
                    results.push(`${format.name}: ❌ 网络错误 - ${error.message}`);
                }

                results.push('---');
            }

            showResult(results.join('\n'), results.some(r => r.includes('✅')) ? 'success' : 'error');
        }

        async function testMCPFormat() {
            const apiKey = document.getElementById('apiKey').value.trim();
            const apiUrl = document.getElementById('apiUrl').value.trim();
            const message = document.getElementById('testMessage').value.trim() || '你好';

            if (!apiKey || !apiUrl) {
                showResult('请填写API密钥和端点', 'error');
                return;
            }

            showResult('正在测试MCP格式和不同的认证方式...', 'info');

            // 基于您提供的URL，测试不同的格式
            const testConfigs = [
                {
                    name: 'MCP格式 - Bearer Token',
                    url: apiUrl,
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${apiKey}`,
                        'Accept': 'application/json'
                    },
                    body: {
                        query: message,
                        user: 'test-user'
                    }
                },
                {
                    name: 'MCP格式 - API Key Header',
                    url: apiUrl,
                    headers: {
                        'Content-Type': 'application/json',
                        'X-API-Key': apiKey,
                        'Accept': 'application/json'
                    },
                    body: {
                        query: message,
                        user: 'test-user'
                    }
                },
                {
                    name: '标准Dify格式 - 修正URL',
                    url: 'https://api.dify.ai/v1/chat-messages',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${apiKey}`,
                        'Accept': 'application/json'
                    },
                    body: {
                        inputs: {},
                        query: message,
                        response_mode: 'blocking',
                        conversation_id: '',
                        user: 'test-user'
                    }
                },
                {
                    name: '应用ID格式 - 使用shSR4iUdPWyYKM0n',
                    url: 'https://api.dify.ai/v1/chat-messages',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer app-${apiKey}`,
                        'Accept': 'application/json'
                    },
                    body: {
                        inputs: {},
                        query: message,
                        response_mode: 'blocking',
                        conversation_id: '',
                        user: 'test-user'
                    }
                }
            ];

            let results = [];

            for (let config of testConfigs) {
                try {
                    console.log(`测试${config.name}:`, {
                        url: config.url,
                        headers: config.headers,
                        body: config.body
                    });

                    const response = await fetch(config.url, {
                        method: 'POST',
                        headers: config.headers,
                        body: JSON.stringify(config.body)
                    });

                    const responseText = await response.text();
                    let responseData;

                    try {
                        responseData = JSON.parse(responseText);
                    } catch (e) {
                        responseData = responseText;
                    }

                    results.push(`${config.name}:`);
                    results.push(`状态: ${response.status} - ${response.ok ? '✅ 成功' : '❌ 失败'}`);

                    if (response.ok) {
                        results.push(`✅ 成功响应: ${JSON.stringify(responseData, null, 2)}`);
                        results.push(`🎉 找到正确格式！请使用此配置。`);
                        break; // 找到成功的格式就停止
                    } else {
                        results.push(`错误详情: ${JSON.stringify(responseData, null, 2)}`);
                    }

                } catch (error) {
                    results.push(`${config.name}: ❌ 网络错误 - ${error.message}`);
                }

                results.push('---');
            }

            showResult(results.join('\n'), results.some(r => r.includes('✅ 成功响应')) ? 'success' : 'error');
        }

        // 页面加载时自动验证配置
        window.addEventListener('load', () => {
            // 尝试从config.js加载配置
            if (typeof DIFY_CONFIG !== 'undefined') {
                document.getElementById('apiKey').value = DIFY_CONFIG.API_KEY;
                document.getElementById('apiUrl').value = DIFY_CONFIG.API_URL;
                validateConfig();
            }
        });
    </script>
    
    <!-- 尝试加载配置文件 -->
    <script src="config.js" onerror="console.log('无法加载config.js')"></script>
</body>
</html>
