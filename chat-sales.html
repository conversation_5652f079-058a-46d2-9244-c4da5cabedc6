<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>销售助理专家 - AGI2B</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .chat-container {
            width: 90%;
            max-width: 900px;
            height: 90vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            color: #333;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .service-icon {
            font-size: 2rem;
        }

        .service-info h1 {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .service-info p {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .header-actions {
            display: flex;
            gap: 10px;
        }

        .action-button {
            background: rgba(255, 255, 255, 0.3);
            color: #333;
            border: none;
            padding: 8px 15px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background 0.3s;
        }

        .action-button:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
            line-height: 1.5;
        }

        .message.user .message-content {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            color: #333;
            border-bottom-right-radius: 4px;
        }

        .message.assistant .message-content {
            background: white;
            color: #333;
            border: 1px solid #e0e0e0;
            border-bottom-left-radius: 4px;
        }

        .chat-input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #e0e0e0;
        }

        .input-group {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .chat-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            outline: none;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .chat-input:focus {
            border-color: #a8edea;
        }

        .send-button {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            color: #333;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: opacity 0.3s;
        }

        .send-button:hover {
            opacity: 0.9;
        }

        .send-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .file-upload-area {
            margin-bottom: 15px;
            padding: 15px;
            border: 2px dashed #e0e0e0;
            border-radius: 10px;
            text-align: center;
            cursor: pointer;
            transition: border-color 0.3s;
        }

        .file-upload-area:hover {
            border-color: #a8edea;
        }

        .file-upload-area.dragover {
            border-color: #a8edea;
            background: rgba(168, 237, 234, 0.1);
        }

        .uploaded-files {
            margin-top: 10px;
        }

        .file-item {
            display: inline-block;
            background: #f0f0f0;
            padding: 5px 10px;
            border-radius: 15px;
            margin: 5px;
            font-size: 0.9rem;
        }

        .typing-indicator {
            display: none;
            padding: 10px 16px;
            background: white;
            border-radius: 18px;
            border: 1px solid #e0e0e0;
            max-width: 70px;
            margin-bottom: 15px;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dots span {
            width: 8px;
            height: 8px;
            background: #999;
            border-radius: 50%;
            animation: typing 1.4s infinite;
        }

        .typing-dots span:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-dots span:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typing {
            0%, 60%, 100% {
                transform: translateY(0);
            }
            30% {
                transform: translateY(-10px);
            }
        }

        .error-message {
            background: #ffebee;
            color: #c62828;
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #c62828;
        }

        .knowledge-status {
            background: rgba(168, 237, 234, 0.1);
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 15px;
            font-size: 0.9rem;
            text-align: center;
        }

        .sales-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin-bottom: 15px;
        }

        .metric-card {
            background: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            border: 1px solid #e0e0e0;
        }

        .metric-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #333;
        }

        .metric-label {
            font-size: 0.8rem;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <div class="header-left">
                <div class="service-icon">🤝</div>
                <div class="service-info">
                    <h1>销售助理专家</h1>
                    <p>AI驱动的销售流程优化，提升转化率</p>
                </div>
            </div>
            <div class="header-actions">
                <button class="action-button" onclick="goBack()">← 返回</button>
                <button class="action-button" onclick="clearChat()">清空对话</button>
            </div>
        </div>
        
        <div class="chat-messages" id="chatMessages">
            <div class="knowledge-status">
                📚 销售策略知识库已加载 | 🤖 RAG智能检索已启用
            </div>
            
            <div class="sales-metrics">
                <div class="metric-card">
                    <div class="metric-value">85%</div>
                    <div class="metric-label">转化率优化</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">24/7</div>
                    <div class="metric-label">智能支持</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">50+</div>
                    <div class="metric-label">销售策略</div>
                </div>
            </div>
            
            <div class="message assistant">
                <div class="message-content">
                    您好！我是销售助理专家，专注于帮助企业优化销售流程、提升客户转化率、管理客户关系。
                    
                    我可以帮助您：
                    • 分析销售漏斗和转化率
                    • 制定个性化销售策略
                    • 优化客户跟进流程
                    • 分析竞争对手策略
                    • 提供销售话术建议
                    • CRM系统优化建议
                    
                    请上传销售数据或直接提问，我将基于专业销售知识库为您提供精准建议！
                </div>
            </div>
        </div>

        <div class="typing-indicator" id="typingIndicator">
            <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>

        <div class="chat-input-container">
            <div class="file-upload-area" id="fileUploadArea">
                📊 点击或拖拽上传销售数据到知识库 (支持Excel、CSV、PDF等格式)
                <input type="file" id="fileInput" style="display: none;" multiple accept=".pdf,.doc,.docx,.txt,.md,.xlsx,.csv">
                <div class="uploaded-files" id="uploadedFiles"></div>
            </div>
            
            <div class="input-group">
                <input 
                    type="text" 
                    class="chat-input" 
                    id="chatInput" 
                    placeholder="请输入您的销售问题..."
                    maxlength="1000"
                >
                <button class="send-button" id="sendButton">发送</button>
            </div>
        </div>
    </div>

    <script>
        // 服务配置
        const SERVICE_CONFIG = {
            name: '销售助理专家',
            type: 'sales',
            apiKey: 'app-your-sales-api-key', // 需要替换为实际的API密钥
            apiUrl: 'https://cloud.dify.ai/v1/chat-messages',
            welcomeMessage: '您好！我是销售助理专家，专注于帮助企业优化销售流程、提升客户转化率、管理客户关系。',
            placeholder: '请输入您的销售问题...'
        };

        // 初始化聊天功能
        function initChat() {
            const chatInput = document.getElementById('chatInput');
            const sendButton = document.getElementById('sendButton');
            
            sendButton.addEventListener('click', sendMessage);
            chatInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });
        }

        // 发送消息
        function sendMessage() {
            const chatInput = document.getElementById('chatInput');
            const message = chatInput.value.trim();
            
            if (!message) return;
            
            addMessage(message, 'user');
            chatInput.value = '';
            
            // 显示打字指示器
            showTypingIndicator();
            
            // 模拟API调用（实际使用时需要连接到Dify API）
            setTimeout(() => {
                hideTypingIndicator();
                const response = generateSalesResponse(message);
                addMessage(response, 'assistant');
            }, 1500);
        }

        // 生成销售相关回复（示例）
        function generateSalesResponse(message) {
            const lowerMessage = message.toLowerCase();

            if (lowerMessage.includes('转化率') || lowerMessage.includes('转化')) {
                return "转化率优化建议：\n1. 分析客户流失节点，重点优化关键转化环节\n2. A/B测试不同销售话术和策略\n3. 建立客户评分模型，优先跟进高意向客户\n4. 优化销售流程，减少客户决策阻力\n\n需要我详细分析某个具体环节吗？";
            } else if (lowerMessage.includes('客户') || lowerMessage.includes('CRM')) {
                return "客户关系管理建议：\n1. 建立完整的客户画像和标签体系\n2. 设计客户生命周期管理流程\n3. 制定差异化的客户服务策略\n4. 建立客户满意度跟踪机制\n\n您希望重点了解哪个方面的客户管理策略？";
            } else if (lowerMessage.includes('销售') || lowerMessage.includes('话术')) {
                return "销售策略优化建议：\n1. 根据客户类型制定个性化销售话术\n2. 建立标准化的销售流程和SOP\n3. 培训销售团队的专业技能\n4. 建立销售数据分析和反馈机制\n\n需要我提供具体的话术模板或销售技巧吗？";
            } else {
                const responses = [
                    "基于销售数据分析，我建议您重点关注：1) 优化销售漏斗的关键节点 2) 提升客户跟进频率 3) 个性化销售话术。需要我详细分析哪个环节？",
                    "从转化率角度看，您的问题涉及到客户生命周期管理。建议建立客户分级体系，针对不同级别客户制定差异化的销售策略。",
                    "销售流程优化需要考虑：客户画像、需求分析、竞品对比、价格策略等。我可以帮您制定具体的销售SOP和话术模板。"
                ];
                return responses[Math.floor(Math.random() * responses.length)];
            }
        }

        // 添加消息到聊天界面
        function addMessage(content, type) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            contentDiv.textContent = content;
            
            messageDiv.appendChild(contentDiv);
            chatMessages.appendChild(messageDiv);
            
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // 显示/隐藏打字指示器
        function showTypingIndicator() {
            document.getElementById('typingIndicator').style.display = 'block';
        }

        function hideTypingIndicator() {
            document.getElementById('typingIndicator').style.display = 'none';
        }

        // 文件上传功能
        function initFileUpload() {
            const fileUploadArea = document.getElementById('fileUploadArea');
            const fileInput = document.getElementById('fileInput');
            const uploadedFiles = document.getElementById('uploadedFiles');

            fileUploadArea.addEventListener('click', () => fileInput.click());
            
            fileInput.addEventListener('change', handleFileUpload);
            
            // 拖拽上传
            fileUploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                fileUploadArea.classList.add('dragover');
            });
            
            fileUploadArea.addEventListener('dragleave', () => {
                fileUploadArea.classList.remove('dragover');
            });
            
            fileUploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                fileUploadArea.classList.remove('dragover');
                handleFileUpload({ target: { files: e.dataTransfer.files } });
            });
        }

        // 处理文件上传
        function handleFileUpload(event) {
            const files = event.target.files;
            const uploadedFiles = document.getElementById('uploadedFiles');
            
            for (let file of files) {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.textContent = `📊 ${file.name}`;
                uploadedFiles.appendChild(fileItem);
                
                // 这里应该调用实际的文件上传API
                console.log('上传销售数据到知识库:', file.name);
            }
        }

        // 返回服务列表
        function goBack() {
            window.location.href = 'services.html';
        }

        // 清空对话
        function clearChat() {
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.innerHTML = `
                <div class="knowledge-status">
                    📚 销售策略知识库已加载 | 🤖 RAG智能检索已启用
                </div>
                
                <div class="sales-metrics">
                    <div class="metric-card">
                        <div class="metric-value">85%</div>
                        <div class="metric-label">转化率优化</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">24/7</div>
                        <div class="metric-label">智能支持</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">50+</div>
                        <div class="metric-label">销售策略</div>
                    </div>
                </div>
                
                <div class="message assistant">
                    <div class="message-content">
                        您好！我是销售助理专家，专注于帮助企业优化销售流程、提升客户转化率、管理客户关系。
                        
                        我可以帮助您：
                        • 分析销售漏斗和转化率
                        • 制定个性化销售策略
                        • 优化客户跟进流程
                        • 分析竞争对手策略
                        • 提供销售话术建议
                        • CRM系统优化建议
                        
                        请上传销售数据或直接提问，我将基于专业销售知识库为您提供精准建议！
                    </div>
                </div>
            `;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            initChat();
            initFileUpload();
        });
    </script>
</body>
</html>
