/**
 * 使用Node.js原生https模块测试Dify API
 */

const https = require('https');
const { URL } = require('url');

function makeHttpsRequest(url, options, data) {
    return new Promise((resolve, reject) => {
        const parsedUrl = new URL(url);
        
        const requestOptions = {
            hostname: parsedUrl.hostname,
            port: parsedUrl.port || 443,
            path: parsedUrl.pathname + parsedUrl.search,
            method: options.method || 'GET',
            headers: options.headers || {},
            timeout: 30000 // 30秒超时
        };
        
        console.log('🔗 连接信息:', {
            hostname: requestOptions.hostname,
            port: requestOptions.port,
            path: requestOptions.path,
            method: requestOptions.method
        });
        
        const req = https.request(requestOptions, (res) => {
            let responseData = '';
            
            console.log(`📡 响应状态: ${res.statusCode} ${res.statusMessage}`);
            console.log('📋 响应头:', res.headers);
            
            res.on('data', (chunk) => {
                responseData += chunk;
            });
            
            res.on('end', () => {
                resolve({
                    statusCode: res.statusCode,
                    statusMessage: res.statusMessage,
                    headers: res.headers,
                    data: responseData
                });
            });
        });
        
        req.on('error', (error) => {
            console.error('❌ 请求错误:', error);
            reject(error);
        });
        
        req.on('timeout', () => {
            console.error('⏰ 请求超时');
            req.destroy();
            reject(new Error('Request timeout'));
        });
        
        if (data) {
            req.write(data);
        }
        
        req.end();
    });
}

async function testDifyWithHttps() {
    console.log('🔍 使用HTTPS模块测试Dify API\n');
    
    const apiKey = 'app-ZRuKpcHUrE5E6zTNQLddDCc';
    const apiUrl = 'https://api.dify.ai/v1/workflows/run';
    
    const requestBody = JSON.stringify({
        inputs: {
            query: "测试查询"
        },
        response_mode: "blocking",
        user: "test_user_" + Date.now()
    });
    
    const options = {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Content-Length': Buffer.byteLength(requestBody),
            'User-Agent': 'Node.js/EcologyExpert'
        }
    };
    
    console.log('📤 发送请求到:', apiUrl);
    console.log('📋 请求头:', options.headers);
    console.log('📄 请求体:', requestBody);
    console.log('');
    
    try {
        const response = await makeHttpsRequest(apiUrl, options, requestBody);
        
        console.log('📥 响应数据:', response.data);
        
        if (response.statusCode === 200) {
            console.log('\n✅ API调用成功!');
            try {
                const jsonResponse = JSON.parse(response.data);
                console.log('📊 解析后的响应:', JSON.stringify(jsonResponse, null, 2));
            } catch (e) {
                console.log('⚠️ 响应不是有效的JSON格式');
            }
        } else {
            console.log('\n❌ API调用失败');
            console.log(`状态码: ${response.statusCode}`);
            
            try {
                const errorData = JSON.parse(response.data);
                console.log('错误详情:', errorData);
                
                if (response.statusCode === 401) {
                    console.log('\n🔑 认证问题建议:');
                    console.log('1. 检查API密钥是否正确复制');
                    console.log('2. 确认API密钥是否有效且未过期');
                    console.log('3. 验证工作流是否已发布');
                    console.log('4. 检查API密钥权限设置');
                }
                
            } catch (e) {
                console.log('原始错误响应:', response.data);
            }
        }
        
    } catch (error) {
        console.error('❌ 连接失败:', error.message);
        
        if (error.code === 'ENOTFOUND') {
            console.log('\n🌐 DNS解析失败:');
            console.log('- 检查网络连接');
            console.log('- 确认域名是否正确');
            console.log('- 可能需要配置DNS服务器');
        } else if (error.code === 'ECONNREFUSED') {
            console.log('\n🚫 连接被拒绝:');
            console.log('- 服务器可能不可用');
            console.log('- 检查防火墙设置');
            console.log('- 确认端口是否正确');
        } else if (error.code === 'ETIMEDOUT') {
            console.log('\n⏰ 连接超时:');
            console.log('- 网络延迟过高');
            console.log('- 可能需要配置代理');
            console.log('- 检查网络稳定性');
        }
    }
}

// 运行测试
if (require.main === module) {
    testDifyWithHttps().catch(console.error);
}

module.exports = { testDifyWithHttps };
