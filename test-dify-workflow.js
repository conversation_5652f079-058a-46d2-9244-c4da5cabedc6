/**
 * Dify工作流集成测试脚本
 * 用于验证工作流API连接和响应处理
 */

const DifyWorkflowClient = require('./dify-workflow-client');
const { SERVICES_CONFIG } = require('./services-config');

async function testDifyWorkflow() {
    console.log('🚀 开始测试Dify工作流集成...\n');

    try {
        // 获取生态拓展服务配置
        const ecologyConfig = SERVICES_CONFIG.ecology;

        // 创建工作流客户端实例
        const workflowClient = new DifyWorkflowClient(ecologyConfig);

        console.log('📋 工作流配置信息:');
        console.log(`- API URL: ${ecologyConfig.apiUrl}`);
        console.log(`- 工作流ID: ${ecologyConfig.workflowId}`);
        console.log(`- API密钥: ${ecologyConfig.apiKey.substring(0, 10)}...`);
        console.log('');
        
        // 测试查询
        const testQuery = '请分析一下可持续发展在城市规划中的应用，并提供具体的实施建议。';
        
        console.log('📤 发送测试查询:', testQuery);
        console.log('');
        
        // 调用工作流
        const startTime = Date.now();
        const response = await workflowClient.callWorkflowAPI(testQuery);
        const endTime = Date.now();
        
        console.log(`⏱️ 请求耗时: ${endTime - startTime}ms`);
        console.log('');
        
        // 解析响应
        const parsedResult = workflowClient.parseWorkflowResponse(response);
        
        console.log('✅ 工作流调用成功!');
        console.log('');
        console.log('📋 响应摘要:');
        console.log(`- 回答长度: ${parsedResult.answer.length} 字符`);
        console.log(`- 会话ID: ${parsedResult.conversation_id || '无'}`);
        console.log(`- 工作流运行ID: ${parsedResult.execution_metadata.workflow_run_id || '无'}`);
        console.log(`- 执行时间: ${parsedResult.execution_metadata.execution_time}ms`);
        console.log('');
        
        console.log('📝 工作流回答:');
        console.log('─'.repeat(50));
        console.log(parsedResult.answer);
        console.log('─'.repeat(50));
        
        if (parsedResult.analysis) {
            console.log('\n🔍 分析结果:');
            console.log(parsedResult.analysis);
        }
        
        if (parsedResult.recommendations) {
            console.log('\n💡 建议:');
            console.log(parsedResult.recommendations);
        }
        
        console.log('\n🎉 测试完成 - 工作流集成正常工作!');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.error('');
        console.error('🔧 可能的解决方案:');
        console.error('1. 检查API密钥是否正确');
        console.error('2. 确认工作流ID是否有效');
        console.error('3. 验证工作流是否已发布');
        console.error('4. 检查网络连接');
        console.error('5. 查看Dify控制台的工作流日志');
        
        if (error.message.includes('401')) {
            console.error('\n🔑 API密钥问题:');
            console.error('- 确保使用的是正确的API密钥');
            console.error('- 检查密钥是否已过期');
        }
        
        if (error.message.includes('404')) {
            console.error('\n🔍 工作流不存在:');
            console.error('- 确认工作流ID是否正确');
            console.error('- 检查工作流是否已发布');
        }
        
        process.exit(1);
    }
}

// 运行测试
if (require.main === module) {
    testDifyWorkflow();
}

module.exports = { testDifyWorkflow };
