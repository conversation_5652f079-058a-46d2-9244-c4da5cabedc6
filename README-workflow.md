# AGI2B 生态拓展工作流集成指南

## 🎯 概述

生态拓展服务已升级为基于 Dify 工作流的智能分析系统，提供更专业、更深度的生态分析能力。

## 🔄 工作流 vs 普通聊天应用

| 特性 | 普通聊天应用 | Dify 工作流 |
|------|-------------|-------------|
| 分析深度 | 基础对话 | 多步骤深度分析 |
| 逻辑复杂度 | 简单 | 复杂业务逻辑 |
| 知识整合 | 单一检索 | 多源知识融合 |
| 结果结构化 | 文本回复 | 结构化分析报告 |
| 可定制性 | 有限 | 高度可定制 |

## 🚀 快速开始

### 1. 配置向导
访问 `workflow-setup.html` 页面，按照向导完成配置：
- 创建 Dify 工作流应用
- 设计工作流结构
- 配置输入输出参数
- 获取 API 密钥和工作流 ID

### 2. 手动配置
编辑 `services-config.js` 文件中的生态拓展配置：

```javascript
ecology: {
    name: '生态拓展专家',
    type: 'workflow', // 标识为工作流类型
    apiKey: 'app-your-actual-api-key', // 替换为实际API密钥
    apiUrl: 'https://cloud.dify.ai/v1/workflows/run',
    workflowId: 'your-actual-workflow-id', // 替换为实际工作流ID
    // ... 其他配置
}
```

## 🏗️ 推荐工作流结构

```mermaid
graph TD
    A[🎯 开始节点] --> B[🧠 意图分析]
    B --> C[📚 知识检索]
    C --> D[🔍 条件判断]
    D --> E[📊 生态分析]
    D --> F[🤝 合作伙伴分析]
    D --> G[📈 市场分析]
    E --> H[💡 建议生成]
    F --> H
    G --> H
    H --> I[📤 结果输出]
```

### 节点配置说明

#### 1. 开始节点
- **输入参数**：
  - `query` (文本): 用户问题
  - `context` (文本): 对话上下文
  - `uploaded_files` (数组): 上传文件信息

#### 2. 意图分析节点 (LLM)
- **提示词示例**：
```
分析用户的生态拓展问题，判断问题类型：
1. 生态格局分析
2. 合作伙伴识别
3. 市场机会分析
4. 风险评估
5. 策略制定

用户问题：{{query}}
返回问题类型和关键信息。
```

#### 3. 知识检索节点
- 配置知识库检索
- 设置检索关键词提取
- 配置相关性阈值

#### 4. 条件判断节点
根据意图分析结果，路由到不同的分析分支：
- 生态分析分支
- 合作伙伴分析分支  
- 市场分析分支

#### 5. 分析节点 (LLM)
- **生态分析提示词**：
```
基于以下信息进行生态分析：
问题：{{query}}
知识库信息：{{knowledge_result}}
上传文件：{{uploaded_files}}

请从以下维度分析：
1. 生态格局现状
2. 关键参与者
3. 价值链分析
4. 竞争态势
5. 发展趋势

输出结构化的分析报告。
```

#### 6. 建议生成节点 (LLM)
- **建议生成提示词**：
```
基于生态分析结果，生成具体的行动建议：
分析结果：{{analysis_result}}

请提供：
1. 短期行动计划（3-6个月）
2. 中期战略规划（6-18个月）
3. 长期生态布局（1-3年）
4. 风险控制措施
5. 关键成功指标

建议要具体、可执行、有时间节点。
```

#### 7. 结果输出节点
- **输出参数**：
  - `result` (文本): 主要分析结果
  - `analysis` (文本): 详细分析报告
  - `recommendations` (文本): 具体建议

## 🔧 API 集成

### 工作流客户端使用

```javascript
// 初始化工作流客户端
const workflowClient = new DifyWorkflowClient({
    apiKey: 'app-your-api-key',
    apiUrl: 'https://cloud.dify.ai/v1/workflows/run',
    workflowId: 'your-workflow-id',
    workflowInputs: {
        query: 'query',
        context: 'context',
        user_info: 'user_info'
    },
    workflowOutputs: {
        result: 'result',
        analysis: 'analysis', 
        recommendations: 'recommendations'
    }
});

// 执行工作流
const result = await workflowClient.runWorkflow(
    '请分析我们公司在AI生态中的定位',
    {
        uploaded_files: [/* 文件信息 */],
        user_info: { company: 'ABC公司', industry: '科技' }
    }
);

console.log(result.answer); // 主要结果
console.log(result.analysis); // 详细分析
console.log(result.recommendations); // 建议
```

### 错误处理

```javascript
try {
    const result = await workflowClient.runWorkflow(query);
    // 处理成功结果
} catch (error) {
    if (error.message.includes('timeout')) {
        // 处理超时
    } else if (error.message.includes('401')) {
        // 处理认证错误
    } else {
        // 处理其他错误
    }
}
```

## 📊 监控和调试

### 1. 执行日志
工作流客户端会输出详细的执行日志：
```
🔄 开始执行Dify工作流...
📤 发送工作流请求: {...}
📥 工作流API响应成功: {...}
✨ 工作流结果解析完成: {...}
```

### 2. 性能监控
```javascript
const status = workflowClient.getStatus();
console.log('执行状态:', status);
console.log('Token消耗:', result.execution_metadata.total_tokens);
console.log('执行时间:', result.execution_metadata.execution_time);
```

### 3. 错误诊断
- 检查 API 密钥是否正确
- 验证工作流 ID 格式
- 确认工作流已发布
- 检查输入参数格式
- 查看 Dify 控制台日志

## 🔒 安全注意事项

1. **API 密钥保护**：
   - 不要在前端代码中硬编码 API 密钥
   - 使用环境变量或配置文件
   - 定期轮换 API 密钥

2. **输入验证**：
   - 验证用户输入长度和格式
   - 过滤敏感信息
   - 防止注入攻击

3. **访问控制**：
   - 实现用户认证
   - 限制 API 调用频率
   - 记录操作日志

## 🚀 性能优化

1. **缓存策略**：
   - 缓存常见问题的分析结果
   - 实现会话级缓存
   - 使用 CDN 加速静态资源

2. **并发控制**：
   - 限制同时执行的工作流数量
   - 实现请求队列
   - 设置合理的超时时间

3. **资源优化**：
   - 压缩上传文件
   - 优化知识库检索
   - 减少不必要的 API 调用

## 📈 扩展功能

### 1. 流式输出
```javascript
await workflowClient.runWorkflowStream(
    query,
    (chunk) => {
        // 处理流式数据
        console.log('收到数据块:', chunk);
    }
);
```

### 2. 批量处理
```javascript
const results = await Promise.all([
    workflowClient.runWorkflow(query1),
    workflowClient.runWorkflow(query2),
    workflowClient.runWorkflow(query3)
]);
```

### 3. 自定义节点
- 添加外部 API 调用节点
- 集成第三方数据源
- 实现自定义分析算法

## 🆘 故障排除

### 常见问题

1. **工作流执行失败**
   - 检查 API 密钥和工作流 ID
   - 验证输入参数格式
   - 查看 Dify 控制台错误日志

2. **响应格式错误**
   - 确认输出参数配置正确
   - 检查工作流节点连接
   - 验证 LLM 节点输出格式

3. **性能问题**
   - 优化工作流结构
   - 减少不必要的节点
   - 调整超时设置

### 联系支持
如果遇到问题，请提供：
- 错误日志
- 工作流配置
- 输入参数示例
- 期望的输出格式

---

## 📝 更新日志

### v1.0.0 (2024-01-XX)
- 初始版本发布
- 支持基础工作流集成
- 提供配置向导

### v1.1.0 (计划中)
- 添加流式输出支持
- 实现批量处理功能
- 优化错误处理机制
