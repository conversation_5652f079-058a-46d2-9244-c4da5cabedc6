/**
 * API密钥更新工具
 * 用于安全地更新Dify工作流API密钥
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

function question(prompt) {
    return new Promise((resolve) => {
        rl.question(prompt, resolve);
    });
}

async function updateApiKey() {
    console.log('🔑 Dify工作流API密钥更新工具\n');
    
    try {
        // 读取当前配置
        const configPath = path.join(__dirname, 'services-config.js');
        let configContent = fs.readFileSync(configPath, 'utf8');
        
        console.log('📋 当前配置信息:');
        const currentKeyMatch = configContent.match(/apiKey:\s*'([^']+)'/);
        if (currentKeyMatch) {
            const currentKey = currentKeyMatch[1];
            console.log(`当前API密钥: ${currentKey.substring(0, 10)}...${currentKey.substring(currentKey.length - 5)}`);
        }
        
        const currentWorkflowMatch = configContent.match(/workflowId:\s*'([^']+)'/);
        if (currentWorkflowMatch) {
            console.log(`当前工作流ID: ${currentWorkflowMatch[1]}`);
        }
        
        console.log('\n请从Dify控制台复制完整的API密钥和工作流ID:');
        
        // 获取新的API密钥
        const newApiKey = await question('请输入新的API密钥 (app-开头): ');
        
        if (!newApiKey || !newApiKey.startsWith('app-')) {
            console.log('❌ API密钥格式错误，必须以 app- 开头');
            process.exit(1);
        }
        
        // 获取新的工作流ID
        const newWorkflowId = await question('请输入工作流ID (可选，按回车跳过): ');
        
        // 更新API密钥
        configContent = configContent.replace(
            /apiKey:\s*'[^']+'/,
            `apiKey: '${newApiKey}'`
        );
        
        // 如果提供了工作流ID，也更新它
        if (newWorkflowId && newWorkflowId.trim()) {
            configContent = configContent.replace(
                /workflowId:\s*'[^']+'/,
                `workflowId: '${newWorkflowId.trim()}'`
            );
        }
        
        // 写回文件
        fs.writeFileSync(configPath, configContent, 'utf8');
        
        console.log('\n✅ 配置更新成功!');
        console.log(`新API密钥: ${newApiKey.substring(0, 10)}...${newApiKey.substring(newApiKey.length - 5)}`);
        
        if (newWorkflowId && newWorkflowId.trim()) {
            console.log(`新工作流ID: ${newWorkflowId.trim()}`);
        }
        
        // 询问是否立即测试
        const shouldTest = await question('\n是否立即测试新配置? (y/n): ');
        
        if (shouldTest.toLowerCase() === 'y' || shouldTest.toLowerCase() === 'yes') {
            console.log('\n🚀 开始测试新配置...\n');
            rl.close();
            
            // 运行测试
            const { spawn } = require('child_process');
            const testProcess = spawn('node', ['test-dify-workflow.js'], {
                stdio: 'inherit',
                cwd: __dirname
            });
            
            testProcess.on('close', (code) => {
                if (code === 0) {
                    console.log('\n🎉 测试成功! 工作流配置正常工作。');
                } else {
                    console.log('\n❌ 测试失败，请检查配置。');
                }
            });
        } else {
            console.log('\n💡 您可以稍后运行 "node test-dify-workflow.js" 来测试配置。');
            rl.close();
        }
        
    } catch (error) {
        console.error('❌ 更新配置时出错:', error.message);
        rl.close();
        process.exit(1);
    }
}

// 运行更新工具
if (require.main === module) {
    updateApiKey();
}

module.exports = { updateApiKey };
