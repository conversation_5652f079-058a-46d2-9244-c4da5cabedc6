<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生态拓展专家 - AGI2B</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .chat-container {
            width: 90%;
            max-width: 900px;
            height: 90vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            color: white;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .service-icon {
            font-size: 2rem;
        }

        .service-info h1 {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .service-info p {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .header-actions {
            display: flex;
            gap: 10px;
        }

        .action-button {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background 0.3s;
        }

        .action-button:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
            line-height: 1.5;
        }

        .message.user .message-content {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            color: white;
            border-bottom-right-radius: 4px;
        }

        .message.assistant .message-content {
            background: white;
            color: #333;
            border: 1px solid #e0e0e0;
            border-bottom-left-radius: 4px;
        }

        .chat-input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #e0e0e0;
        }

        .input-group {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .chat-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            outline: none;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .chat-input:focus {
            border-color: #ff9a9e;
        }

        .send-button {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: opacity 0.3s;
        }

        .send-button:hover {
            opacity: 0.9;
        }

        .send-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .file-upload-area {
            margin-bottom: 15px;
            padding: 15px;
            border: 2px dashed #e0e0e0;
            border-radius: 10px;
            text-align: center;
            cursor: pointer;
            transition: border-color 0.3s;
        }

        .file-upload-area:hover {
            border-color: #ff9a9e;
        }

        .file-upload-area.dragover {
            border-color: #ff9a9e;
            background: rgba(255, 154, 158, 0.1);
        }

        .uploaded-files {
            margin-top: 10px;
        }

        .file-item {
            display: inline-block;
            background: #f0f0f0;
            padding: 5px 10px;
            border-radius: 15px;
            margin: 5px;
            font-size: 0.9rem;
        }

        .typing-indicator {
            display: none;
            padding: 10px 16px;
            background: white;
            border-radius: 18px;
            border: 1px solid #e0e0e0;
            max-width: 70px;
            margin-bottom: 15px;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dots span {
            width: 8px;
            height: 8px;
            background: #999;
            border-radius: 50%;
            animation: typing 1.4s infinite;
        }

        .typing-dots span:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-dots span:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typing {
            0%, 60%, 100% {
                transform: translateY(0);
            }
            30% {
                transform: translateY(-10px);
            }
        }

        .error-message {
            background: #ffebee;
            color: #c62828;
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #c62828;
        }

        .knowledge-status {
            background: rgba(255, 154, 158, 0.1);
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 15px;
            font-size: 0.9rem;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <div class="header-left">
                <div class="service-icon">🌱</div>
                <div class="service-info">
                    <h1>生态拓展专家</h1>
                    <p>智能分析市场生态，构建合作伙伴网络</p>
                </div>
            </div>
            <div class="header-actions">
                <button class="action-button" onclick="goBack()">← 返回</button>
                <button class="action-button" onclick="clearChat()">清空对话</button>
            </div>
        </div>
        
        <div class="chat-messages" id="chatMessages">
            <div class="knowledge-status">
                📚 生态拓展知识库已加载 | 🤖 RAG智能检索已启用
            </div>
            
            <div class="message assistant">
                <div class="message-content">
                    您好！我是生态拓展专家，专注于帮助企业分析市场生态、构建合作伙伴网络、制定生态战略。
                    
                    我可以帮助您：
                    • 分析行业生态格局
                    • 识别潜在合作伙伴
                    • 制定生态拓展策略
                    • 评估生态合作风险
                    • 优化生态合作模式
                    
                    请上传相关文档或直接提问，我将基于专业知识库为您提供精准建议！
                </div>
            </div>
        </div>

        <div class="typing-indicator" id="typingIndicator">
            <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>

        <div class="chat-input-container">
            <div class="file-upload-area" id="fileUploadArea">
                📎 点击或拖拽上传文档到知识库 (支持PDF、Word、TXT等格式)
                <input type="file" id="fileInput" style="display: none;" multiple accept=".pdf,.doc,.docx,.txt,.md">
                <div class="uploaded-files" id="uploadedFiles"></div>
            </div>
            
            <div class="input-group">
                <input 
                    type="text" 
                    class="chat-input" 
                    id="chatInput" 
                    placeholder="请输入您的生态拓展问题..."
                    maxlength="1000"
                >
                <button class="send-button" id="sendButton">发送</button>
            </div>
        </div>
    </div>

    <script>
        // 服务配置
        const SERVICE_CONFIG = {
            name: '生态拓展专家',
            type: 'ecology',
            apiKey: 'app-your-ecology-api-key', // 需要替换为实际的API密钥
            apiUrl: 'https://cloud.dify.ai/v1/chat-messages',
            welcomeMessage: '您好！我是生态拓展专家，专注于帮助企业分析市场生态、构建合作伙伴网络、制定生态战略。',
            placeholder: '请输入您的生态拓展问题...'
        };

        // 初始化聊天功能
        function initChat() {
            const chatInput = document.getElementById('chatInput');
            const sendButton = document.getElementById('sendButton');
            
            sendButton.addEventListener('click', sendMessage);
            chatInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });
        }

        // 发送消息
        function sendMessage() {
            const chatInput = document.getElementById('chatInput');
            const message = chatInput.value.trim();
            
            if (!message) return;
            
            addMessage(message, 'user');
            chatInput.value = '';
            
            // 显示打字指示器
            showTypingIndicator();
            
            // 模拟API调用（实际使用时需要连接到Dify API）
            setTimeout(() => {
                hideTypingIndicator();
                const response = generateEcologyResponse(message);
                addMessage(response, 'assistant');
            }, 1500);
        }

        // 生成生态拓展相关回复（示例）
        function generateEcologyResponse(message) {
            const responses = [
                "基于生态分析，我建议您重点关注以下几个方面：1) 上下游产业链整合 2) 技术生态伙伴合作 3) 渠道生态建设。需要我详细分析某个方面吗？",
                "从市场生态角度看，您的问题涉及到生态位定位。建议先明确自身在生态中的核心价值，然后识别关键合作伙伴，最后制定差异化的生态策略。",
                "生态拓展需要考虑多个维度：市场规模、竞争格局、技术趋势、政策环境等。我可以帮您分析具体的生态机会和风险评估。"
            ];
            return responses[Math.floor(Math.random() * responses.length)];
        }

        // 添加消息到聊天界面
        function addMessage(content, type) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            contentDiv.textContent = content;
            
            messageDiv.appendChild(contentDiv);
            chatMessages.appendChild(messageDiv);
            
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // 显示/隐藏打字指示器
        function showTypingIndicator() {
            document.getElementById('typingIndicator').style.display = 'block';
        }

        function hideTypingIndicator() {
            document.getElementById('typingIndicator').style.display = 'none';
        }

        // 文件上传功能
        function initFileUpload() {
            const fileUploadArea = document.getElementById('fileUploadArea');
            const fileInput = document.getElementById('fileInput');
            const uploadedFiles = document.getElementById('uploadedFiles');

            fileUploadArea.addEventListener('click', () => fileInput.click());
            
            fileInput.addEventListener('change', handleFileUpload);
            
            // 拖拽上传
            fileUploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                fileUploadArea.classList.add('dragover');
            });
            
            fileUploadArea.addEventListener('dragleave', () => {
                fileUploadArea.classList.remove('dragover');
            });
            
            fileUploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                fileUploadArea.classList.remove('dragover');
                handleFileUpload({ target: { files: e.dataTransfer.files } });
            });
        }

        // 处理文件上传
        function handleFileUpload(event) {
            const files = event.target.files;
            const uploadedFiles = document.getElementById('uploadedFiles');
            
            for (let file of files) {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.textContent = `📄 ${file.name}`;
                uploadedFiles.appendChild(fileItem);
                
                // 这里应该调用实际的文件上传API
                console.log('上传文件到知识库:', file.name);
            }
        }

        // 返回服务列表
        function goBack() {
            window.location.href = 'services.html';
        }

        // 清空对话
        function clearChat() {
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.innerHTML = `
                <div class="knowledge-status">
                    📚 生态拓展知识库已加载 | 🤖 RAG智能检索已启用
                </div>
                
                <div class="message assistant">
                    <div class="message-content">
                        您好！我是生态拓展专家，专注于帮助企业分析市场生态、构建合作伙伴网络、制定生态战略。
                        
                        我可以帮助您：
                        • 分析行业生态格局
                        • 识别潜在合作伙伴
                        • 制定生态拓展策略
                        • 评估生态合作风险
                        • 优化生态合作模式
                        
                        请上传相关文档或直接提问，我将基于专业知识库为您提供精准建议！
                    </div>
                </div>
            `;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            initChat();
            initFileUpload();
        });
    </script>
</body>
</html>
