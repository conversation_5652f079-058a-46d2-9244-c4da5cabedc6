<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>售前支持专家 - AGI2B</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #a8c8ec 0%, #7fcdff 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .chat-container {
            width: 90%;
            max-width: 900px;
            height: 90vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(135deg, #a8c8ec 0%, #7fcdff 100%);
            color: white;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .service-icon {
            font-size: 2rem;
        }

        .service-info h1 {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .service-info p {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .header-actions {
            display: flex;
            gap: 10px;
        }

        .action-button {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background 0.3s;
        }

        .action-button:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
            line-height: 1.5;
        }

        .message.user .message-content {
            background: linear-gradient(135deg, #a8c8ec 0%, #7fcdff 100%);
            color: white;
            border-bottom-right-radius: 4px;
        }

        .message.assistant .message-content {
            background: white;
            color: #333;
            border: 1px solid #e0e0e0;
            border-bottom-left-radius: 4px;
        }

        .chat-input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #e0e0e0;
        }

        .input-group {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .chat-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            outline: none;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .chat-input:focus {
            border-color: #7fcdff;
        }

        .send-button {
            background: linear-gradient(135deg, #a8c8ec 0%, #7fcdff 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: opacity 0.3s;
        }

        .send-button:hover {
            opacity: 0.9;
        }

        .send-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .file-upload-area {
            margin-bottom: 15px;
            padding: 15px;
            border: 2px dashed #e0e0e0;
            border-radius: 10px;
            text-align: center;
            cursor: pointer;
            transition: border-color 0.3s;
        }

        .file-upload-area:hover {
            border-color: #7fcdff;
        }

        .file-upload-area.dragover {
            border-color: #7fcdff;
            background: rgba(127, 205, 255, 0.1);
        }

        .uploaded-files {
            margin-top: 10px;
        }

        .file-item {
            display: inline-block;
            background: #f0f0f0;
            padding: 5px 10px;
            border-radius: 15px;
            margin: 5px;
            font-size: 0.9rem;
        }

        .typing-indicator {
            display: none;
            padding: 10px 16px;
            background: white;
            border-radius: 18px;
            border: 1px solid #e0e0e0;
            max-width: 70px;
            margin-bottom: 15px;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dots span {
            width: 8px;
            height: 8px;
            background: #999;
            border-radius: 50%;
            animation: typing 1.4s infinite;
        }

        .typing-dots span:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-dots span:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typing {
            0%, 60%, 100% {
                transform: translateY(0);
            }
            30% {
                transform: translateY(-10px);
            }
        }

        .error-message {
            background: #ffebee;
            color: #c62828;
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #c62828;
        }

        .knowledge-status {
            background: rgba(127, 205, 255, 0.1);
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 15px;
            font-size: 0.9rem;
            text-align: center;
        }

        .support-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin-bottom: 15px;
        }

        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            border: 1px solid #e0e0e0;
        }

        .stat-value {
            font-size: 1.2rem;
            font-weight: bold;
            color: #7fcdff;
        }

        .stat-label {
            font-size: 0.8rem;
            color: #666;
            margin-top: 5px;
        }

        .quick-actions {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .quick-action {
            background: rgba(127, 205, 255, 0.1);
            color: #333;
            border: 1px solid #7fcdff;
            padding: 8px 15px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background 0.3s;
        }

        .quick-action:hover {
            background: rgba(127, 205, 255, 0.2);
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <div class="header-left">
                <div class="service-icon">💬</div>
                <div class="service-info">
                    <h1>售前支持专家</h1>
                    <p>智能客户需求分析，方案定制，提升成单率</p>
                </div>
            </div>
            <div class="header-actions">
                <button class="action-button" onclick="goBack()">← 返回</button>
                <button class="action-button" onclick="clearChat()">清空对话</button>
            </div>
        </div>
        
        <div class="chat-messages" id="chatMessages">
            <div class="knowledge-status">
                📚 售前支持知识库已加载 | 🤖 RAG智能检索已启用
            </div>
            
            <div class="support-stats">
                <div class="stat-card">
                    <div class="stat-value">95%</div>
                    <div class="stat-label">客户满意度</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">2min</div>
                    <div class="stat-label">平均响应</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">80%</div>
                    <div class="stat-label">成单率提升</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">24/7</div>
                    <div class="stat-label">在线支持</div>
                </div>
            </div>

            <div class="quick-actions">
                <div class="quick-action" onclick="askQuestion('产品介绍')">产品介绍</div>
                <div class="quick-action" onclick="askQuestion('价格咨询')">价格咨询</div>
                <div class="quick-action" onclick="askQuestion('技术支持')">技术支持</div>
                <div class="quick-action" onclick="askQuestion('方案定制')">方案定制</div>
                <div class="quick-action" onclick="askQuestion('合作流程')">合作流程</div>
            </div>
            
            <div class="message assistant">
                <div class="message-content">
                    您好！我是售前支持专家，专注于帮助客户了解产品、分析需求、定制解决方案。
                    
                    我可以帮助您：
                    • 详细介绍产品功能和优势
                    • 分析客户需求和痛点
                    • 制定个性化解决方案
                    • 提供技术咨询和支持
                    • 协助商务谈判和合同签署
                    • 解答各类售前疑问
                    
                    请点击快捷按钮或直接提问，我将为您提供专业的售前支持服务！
                </div>
            </div>
        </div>

        <div class="typing-indicator" id="typingIndicator">
            <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>

        <div class="chat-input-container">
            <div class="file-upload-area" id="fileUploadArea">
                💼 点击或拖拽上传客户资料到知识库 (支持需求文档、RFP等)
                <input type="file" id="fileInput" style="display: none;" multiple accept=".pdf,.doc,.docx,.txt,.md,.xlsx">
                <div class="uploaded-files" id="uploadedFiles"></div>
            </div>
            
            <div class="input-group">
                <input 
                    type="text" 
                    class="chat-input" 
                    id="chatInput" 
                    placeholder="请输入您的售前咨询问题..."
                    maxlength="1000"
                >
                <button class="send-button" id="sendButton">发送</button>
            </div>
        </div>
    </div>

    <script>
        // 服务配置
        const SERVICE_CONFIG = {
            name: '售前支持专家',
            type: 'support',
            apiKey: 'app-your-support-api-key', // 需要替换为实际的API密钥
            apiUrl: 'https://cloud.dify.ai/v1/chat-messages',
            welcomeMessage: '您好！我是售前支持专家，专注于帮助客户了解产品、分析需求、定制解决方案。',
            placeholder: '请输入您的售前咨询问题...'
        };

        // 初始化聊天功能
        function initChat() {
            const chatInput = document.getElementById('chatInput');
            const sendButton = document.getElementById('sendButton');
            
            sendButton.addEventListener('click', sendMessage);
            chatInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });
        }

        // 快捷提问
        function askQuestion(question) {
            const chatInput = document.getElementById('chatInput');
            chatInput.value = question;
            sendMessage();
        }

        // 发送消息
        function sendMessage() {
            const chatInput = document.getElementById('chatInput');
            const message = chatInput.value.trim();

            if (!message) return;

            addMessage(message, 'user');
            chatInput.value = '';

            // 显示打字指示器
            showTypingIndicator();

            // 模拟API调用（实际使用时需要连接到Dify API）
            setTimeout(() => {
                hideTypingIndicator();
                const response = generateSupportResponse(message);
                addMessage(response, 'assistant');
            }, 1500);
        }

        // 生成售前支持相关回复（示例）
        function generateSupportResponse(message) {
            const lowerMessage = message.toLowerCase();

            if (lowerMessage.includes('产品介绍') || lowerMessage.includes('功能')) {
                return "产品介绍：\n我们的AGI2B平台提供四大核心服务：\n\n🌱 生态拓展：智能分析市场生态，构建合作伙伴网络\n🤝 销售助理：AI驱动的销售流程优化，提升转化率\n🚀 产品研发：市场需求分析，产品定制，提升竞争力\n💬 售前支持：智能客户需求分析，方案定制\n\n每个服务都配备专业知识库和RAG智能检索功能。您对哪个服务最感兴趣？";
            } else if (lowerMessage.includes('价格') || lowerMessage.includes('费用') || lowerMessage.includes('成本')) {
                return "价格方案：\n我们提供灵活的定价模式：\n\n💎 基础版：适合小团队，包含核心功能\n🚀 专业版：适合中型企业，包含高级功能和定制服务\n🏢 企业版：适合大型企业，包含全功能和专属支持\n\n具体价格会根据您的使用规模、功能需求和服务级别来定制。我可以为您安排详细的报价演示，请问您的团队规模大概是多少？";
            } else if (lowerMessage.includes('技术') || lowerMessage.includes('集成') || lowerMessage.includes('API')) {
                return "技术支持：\n我们提供全面的技术支持：\n\n🔧 API集成：提供完整的API文档和SDK\n🛠️ 系统集成：支持与现有系统无缝对接\n📚 技术文档：详细的开发指南和最佳实践\n👨‍💻 技术支持：专业技术团队7x24小时支持\n\n我们支持多种集成方式，包括REST API、Webhook、SDK等。您希望了解哪种集成方式？";
            } else if (lowerMessage.includes('方案') || lowerMessage.includes('定制')) {
                return "方案定制：\n我们为每个客户提供个性化解决方案：\n\n📋 需求分析：深入了解您的业务场景和痛点\n🎯 方案设计：基于需求设计最适合的解决方案\n🔧 功能定制：根据特殊需求进行功能定制开发\n📈 效果评估：建立KPI指标，持续优化方案效果\n\n为了给您提供最合适的方案，请简单介绍一下您的业务场景和主要需求？";
            } else if (lowerMessage.includes('合作') || lowerMessage.includes('流程')) {
                return "合作流程：\n我们的标准合作流程如下：\n\n1️⃣ 需求沟通：了解您的具体需求和期望\n2️⃣ 方案设计：制定个性化解决方案\n3️⃣ 演示验证：产品演示和POC验证\n4️⃣ 商务谈判：确定合作条款和价格\n5️⃣ 合同签署：签署正式合作协议\n6️⃣ 项目实施：系统部署和培训\n7️⃣ 持续服务：后续支持和优化\n\n整个流程通常需要2-4周时间。您目前处于哪个阶段？";
            } else {
                const responses = [
                    "感谢您的咨询！我是售前支持专家，可以为您提供产品介绍、价格咨询、技术支持、方案定制等服务。请问您最关心哪个方面？",
                    "我很乐意为您提供专业的售前支持。为了给您最准确的建议，请告诉我您的具体需求或关注点？",
                    "作为售前支持专家，我可以帮您分析需求、制定方案、解答疑问。请问您希望了解我们的哪项服务？"
                ];
                return responses[Math.floor(Math.random() * responses.length)];
            }
        }

        // 添加消息到聊天界面
        function addMessage(content, type) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;

            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            contentDiv.textContent = content;

            messageDiv.appendChild(contentDiv);
            chatMessages.appendChild(messageDiv);

            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // 显示/隐藏打字指示器
        function showTypingIndicator() {
            document.getElementById('typingIndicator').style.display = 'block';
        }

        function hideTypingIndicator() {
            document.getElementById('typingIndicator').style.display = 'none';
        }

        // 文件上传功能
        function initFileUpload() {
            const fileUploadArea = document.getElementById('fileUploadArea');
            const fileInput = document.getElementById('fileInput');
            const uploadedFiles = document.getElementById('uploadedFiles');

            fileUploadArea.addEventListener('click', () => fileInput.click());

            fileInput.addEventListener('change', handleFileUpload);

            // 拖拽上传
            fileUploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                fileUploadArea.classList.add('dragover');
            });

            fileUploadArea.addEventListener('dragleave', () => {
                fileUploadArea.classList.remove('dragover');
            });

            fileUploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                fileUploadArea.classList.remove('dragover');
                handleFileUpload({ target: { files: e.dataTransfer.files } });
            });
        }

        // 处理文件上传
        function handleFileUpload(event) {
            const files = event.target.files;
            const uploadedFiles = document.getElementById('uploadedFiles');

            for (let file of files) {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.textContent = `💼 ${file.name}`;
                uploadedFiles.appendChild(fileItem);

                // 这里应该调用实际的文件上传API
                console.log('上传客户资料到知识库:', file.name);
            }
        }

        // 返回服务列表
        function goBack() {
            window.location.href = 'services.html';
        }

        // 清空对话
        function clearChat() {
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.innerHTML = `
                <div class="knowledge-status">
                    📚 售前支持知识库已加载 | 🤖 RAG智能检索已启用
                </div>

                <div class="support-stats">
                    <div class="stat-card">
                        <div class="stat-value">95%</div>
                        <div class="stat-label">客户满意度</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">2min</div>
                        <div class="stat-label">平均响应</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">80%</div>
                        <div class="stat-label">成单率提升</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">24/7</div>
                        <div class="stat-label">在线支持</div>
                    </div>
                </div>

                <div class="quick-actions">
                    <div class="quick-action" onclick="askQuestion('产品介绍')">产品介绍</div>
                    <div class="quick-action" onclick="askQuestion('价格咨询')">价格咨询</div>
                    <div class="quick-action" onclick="askQuestion('技术支持')">技术支持</div>
                    <div class="quick-action" onclick="askQuestion('方案定制')">方案定制</div>
                    <div class="quick-action" onclick="askQuestion('合作流程')">合作流程</div>
                </div>

                <div class="message assistant">
                    <div class="message-content">
                        您好！我是售前支持专家，专注于帮助客户了解产品、分析需求、定制解决方案。

                        我可以帮助您：
                        • 详细介绍产品功能和优势
                        • 分析客户需求和痛点
                        • 制定个性化解决方案
                        • 提供技术咨询和支持
                        • 协助商务谈判和合同签署
                        • 解答各类售前疑问

                        请点击快捷按钮或直接提问，我将为您提供专业的售前支持服务！
                    </div>
                </div>
            `;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            initChat();
            initFileUpload();
        });
    </script>
</body>
</html>
