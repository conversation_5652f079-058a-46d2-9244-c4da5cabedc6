// AGI2B 服务配置文件
// 请在此文件中配置各个服务的API密钥和设置

const SERVICES_CONFIG = {
    // 生态拓展服务配置 - 使用Dify工作流
    ecology: {
        name: '生态拓展专家',
        type: 'workflow', // 标识为工作流类型
        apiKey: 'app-ZRuKpcHUrE5E6zTNQLddDCc', // 实际的Dify工作流API密钥
        apiUrl: 'https://api.dify.ai/v1/workflows/run', // 工作流API端点
        workflowId: '8cc0dc2f-37ed-4dbd-b647-0fc278810788', // 实际的工作流ID
        description: '智能分析市场生态，构建合作伙伴网络，扩大业务生态圈',
        features: ['AI分析', '合作伙伴', '生态布局'],
        knowledgeBase: '生态拓展知识库',
        fileTypes: ['.pdf', '.doc', '.docx', '.txt', '.md'],
        maxFileSize: 10 * 1024 * 1024, // 10MB
        // 工作流输入参数配置
        workflowInputs: {
            query: 'query', // 用户输入的参数名
            context: 'context', // 上下文参数名（可选）
            user_info: 'user_info' // 用户信息参数名（可选）
        },
        // 工作流输出参数配置
        workflowOutputs: {
            result: 'result', // 主要结果输出参数名
            analysis: 'analysis', // 分析结果参数名（可选）
            recommendations: 'recommendations' // 建议输出参数名（可选）
        },
        welcomeMessage: `您好！我是生态拓展专家，基于Dify智能工作流为您提供专业的生态分析服务。

🔄 工作流功能：
• 智能分析市场生态格局
• 识别潜在合作伙伴
• 制定生态拓展策略
• 评估生态合作风险
• 生成生态合作建议

请上传相关文档或直接提问，我将通过专业的AI工作流为您提供深度分析！`
    },

    // 销售助理服务配置
    sales: {
        name: '销售助理专家',
        apiKey: 'app-your-sales-api-key-here', // 请替换为实际的Dify API密钥
        apiUrl: 'https://cloud.dify.ai/v1/chat-messages',
        description: 'AI驱动的销售流程优化，客户关系管理，提升转化率',
        features: ['营销优化', 'CRM管理', '转化分析'],
        knowledgeBase: '销售策略知识库',
        fileTypes: ['.pdf', '.doc', '.docx', '.txt', '.md', '.xlsx', '.csv'],
        maxFileSize: 10 * 1024 * 1024, // 10MB
        welcomeMessage: `您好！我是销售助理专家，专注于帮助企业优化销售流程、提升客户转化率、管理客户关系。

我可以帮助您：
• 分析销售漏斗和转化率
• 制定个性化销售策略
• 优化客户跟进流程
• 分析竞争对手策略
• 提供销售话术建议
• CRM系统优化建议

请上传销售数据或直接提问，我将基于专业销售知识库为您提供精准建议！`
    },

    // 产品研发服务配置
    product: {
        name: '产品研发专家',
        apiKey: 'app-your-product-api-key-here', // 请替换为实际的Dify API密钥
        apiUrl: 'https://cloud.dify.ai/v1/chat-messages',
        description: '市场需求分析，产品定制，提升产品竞争力',
        features: ['需求分析', '产品定制', '方案定制'],
        knowledgeBase: '产品开发知识库',
        fileTypes: ['.pdf', '.doc', '.docx', '.txt', '.md', '.ppt', '.pptx', '.jpg', '.png'],
        maxFileSize: 20 * 1024 * 1024, // 20MB (支持图片文件)
        welcomeMessage: `您好！我是产品研发专家，专注于帮助企业进行市场需求分析、产品设计优化、技术方案制定。

我可以帮助您：
• 分析市场需求和用户痛点
• 制定产品功能规划和路线图
• 优化产品设计和用户体验
• 评估技术可行性和成本
• 制定产品迭代策略
• 竞品分析和差异化定位

请上传产品文档或直接提问，我将基于专业产品知识库为您提供精准建议！`
    },

    // 售前支持服务配置
    support: {
        name: '售前支持专家',
        apiKey: 'app-your-support-api-key-here', // 请替换为实际的Dify API密钥
        apiUrl: 'https://cloud.dify.ai/v1/chat-messages',
        description: '智能客户需求分析，方案定制，提升成单率',
        features: ['客户需求', '需求分析', '方案定制'],
        knowledgeBase: '售前支持知识库',
        fileTypes: ['.pdf', '.doc', '.docx', '.txt', '.md', '.xlsx'],
        maxFileSize: 10 * 1024 * 1024, // 10MB
        welcomeMessage: `您好！我是售前支持专家，专注于帮助客户了解产品、分析需求、定制解决方案。

我可以帮助您：
• 详细介绍产品功能和优势
• 分析客户需求和痛点
• 制定个性化解决方案
• 提供技术咨询和支持
• 协助商务谈判和合同签署
• 解答各类售前疑问

请点击快捷按钮或直接提问，我将为您提供专业的售前支持服务！`
    }
};

// 通用配置
const COMMON_CONFIG = {
    // 请求配置
    requestTimeout: 30000, // 30秒超时
    maxRetries: 3, // 最大重试次数
    retryDelay: 1000, // 重试延迟(毫秒)

    // 消息配置
    maxMessageLength: 1000, // 最大消息长度
    responseMode: 'blocking', // 响应模式

    // UI配置
    typingDelay: 1500, // 打字指示器延迟
    scrollDelay: 100, // 滚动延迟

    // 文件上传配置
    maxFiles: 5, // 最大文件数量
    allowedFileTypes: ['.pdf', '.doc', '.docx', '.txt', '.md', '.xlsx', '.csv', '.ppt', '.pptx', '.jpg', '.png'],
    
    // 错误消息
    errorMessages: {
        emptyMessage: '请输入消息内容',
        messageTooLong: '消息长度超过限制',
        networkError: '网络连接失败，请检查网络设置',
        apiError: 'API调用失败，请稍后重试',
        timeoutError: '请求超时，请稍后重试',
        invalidResponse: '服务器响应格式错误',
        fileUploadError: '文件上传失败',
        fileSizeError: '文件大小超过限制',
        fileTypeError: '不支持的文件类型'
    }
};

// 验证配置函数
function validateServicesConfig() {
    const errors = [];
    
    for (const [serviceType, config] of Object.entries(SERVICES_CONFIG)) {
        // 检查API密钥
        if (!config.apiKey || config.apiKey.includes('your-') || config.apiKey === '') {
            errors.push(`${serviceType} 服务的API密钥未配置`);
        }
        
        // 检查API密钥格式
        if (config.apiKey && !config.apiKey.startsWith('app-')) {
            errors.push(`${serviceType} 服务的API密钥格式错误（应以 app- 开头）`);
        }
        
        // 检查API URL
        if (!config.apiUrl || !config.apiUrl.startsWith('https://')) {
            errors.push(`${serviceType} 服务的API URL配置错误`);
        }
    }
    
    return {
        isValid: errors.length === 0,
        errors: errors
    };
}

// 获取服务配置
function getServiceConfig(serviceType) {
    if (!SERVICES_CONFIG[serviceType]) {
        throw new Error(`未找到服务类型: ${serviceType}`);
    }
    
    return {
        ...SERVICES_CONFIG[serviceType],
        ...COMMON_CONFIG
    };
}

// 获取所有服务列表
function getAllServices() {
    return Object.keys(SERVICES_CONFIG).map(serviceType => ({
        type: serviceType,
        name: SERVICES_CONFIG[serviceType].name,
        description: SERVICES_CONFIG[serviceType].description,
        features: SERVICES_CONFIG[serviceType].features
    }));
}

// 导出配置（用于浏览器环境）
if (typeof window !== 'undefined') {
    window.SERVICES_CONFIG = SERVICES_CONFIG;
    window.COMMON_CONFIG = COMMON_CONFIG;
    window.validateServicesConfig = validateServicesConfig;
    window.getServiceConfig = getServiceConfig;
    window.getAllServices = getAllServices;
}

// 导出配置（用于Node.js环境）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        SERVICES_CONFIG,
        COMMON_CONFIG,
        validateServicesConfig,
        getServiceConfig,
        getAllServices
    };
}
