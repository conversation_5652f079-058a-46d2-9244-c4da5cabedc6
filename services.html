<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AGI2B - 我们的服务</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .services-container {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }

        .service-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .service-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
        }

        .service-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #4285f4, #34a853, #fbbc05, #ea4335);
        }

        .service-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            text-align: center;
        }

        .service-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 15px;
            text-align: center;
            color: #333;
        }

        .service-description {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
            text-align: center;
        }

        .service-features {
            list-style: none;
            margin-bottom: 25px;
        }

        .service-features li {
            padding: 8px 0;
            color: #555;
            position: relative;
            padding-left: 20px;
        }

        .service-features li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #4285f4;
            font-weight: bold;
        }

        .service-button {
            width: 100%;
            background: #4285f4;
            color: white;
            border: none;
            padding: 15px 20px;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .service-button:hover {
            background: #3367d6;
        }

        .service-card.ecology {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            color: white;
        }

        .service-card.sales {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            color: #333;
        }

        .service-card.product {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            color: #333;
        }

        .service-card.support {
            background: linear-gradient(135deg, #a8c8ec 0%, #7fcdff 100%);
            color: white;
        }

        .service-card.ecology .service-title,
        .service-card.support .service-title {
            color: white;
        }

        .service-card.ecology .service-description,
        .service-card.support .service-description {
            color: rgba(255, 255, 255, 0.9);
        }

        .service-card.ecology .service-features li,
        .service-card.support .service-features li {
            color: rgba(255, 255, 255, 0.8);
        }

        .knowledge-base-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
            font-size: 0.9rem;
        }

        .service-card.sales .knowledge-base-info,
        .service-card.product .knowledge-base-info {
            background: rgba(0, 0, 0, 0.05);
        }

        @media (max-width: 768px) {
            .services-container {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .header p {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>AGI2B</h1>
        <p>我们的服务</p>
    </div>

    <div class="services-container">
        <!-- 生态拓展服务 -->
        <div class="service-card ecology" onclick="openService('ecology')">
            <div class="service-icon">🌱</div>
            <h2 class="service-title">生态拓展</h2>
            <p class="service-description">智能分析市场生态，构建合作伙伴网络，扩大业务生态圈</p>
            
            <div class="knowledge-base-info">
                📚 专业生态知识库 | 🤖 RAG智能检索
            </div>
            
            <ul class="service-features">
                <li>AI分析</li>
                <li>合作伙伴</li>
                <li>生态布局</li>
            </ul>
            
            <button class="service-button">开始咨询</button>
        </div>

        <!-- 销售助理服务 -->
        <div class="service-card sales" onclick="openService('sales')">
            <div class="service-icon">🤝</div>
            <h2 class="service-title">销售助理</h2>
            <p class="service-description">AI驱动的销售流程优化，客户关系管理，提升转化率</p>
            
            <div class="knowledge-base-info">
                📚 销售策略知识库 | 🤖 RAG智能检索
            </div>
            
            <ul class="service-features">
                <li>营销优化</li>
                <li>CRM管理</li>
                <li>转化分析</li>
            </ul>
            
            <button class="service-button">开始咨询</button>
        </div>

        <!-- 产品研发服务 -->
        <div class="service-card product" onclick="openService('product')">
            <div class="service-icon">🚀</div>
            <h2 class="service-title">产品研发</h2>
            <p class="service-description">市场需求分析，需求定制，提升产品竞争力</p>
            
            <div class="knowledge-base-info">
                📚 产品开发知识库 | 🤖 RAG智能检索
            </div>
            
            <ul class="service-features">
                <li>需求分析</li>
                <li>需求定制</li>
                <li>方案定制</li>
            </ul>
            
            <button class="service-button">开始咨询</button>
        </div>

        <!-- 售前支持服务 -->
        <div class="service-card support" onclick="openService('support')">
            <div class="service-icon">💬</div>
            <h2 class="service-title">售前支持</h2>
            <p class="service-description">智能客户需求，需求分析，方案定制，提升单率</p>
            
            <div class="knowledge-base-info">
                📚 售前支持知识库 | 🤖 RAG智能检索
            </div>
            
            <ul class="service-features">
                <li>客户需求</li>
                <li>需求分析</li>
                <li>方案定制</li>
            </ul>
            
            <button class="service-button">开始咨询</button>
        </div>
    </div>

    <script>
        function openService(serviceType) {
            // 根据服务类型跳转到对应的聊天页面
            const serviceUrls = {
                'ecology': 'chat-ecology.html',
                'sales': 'chat-sales.html', 
                'product': 'chat-product.html',
                'support': 'chat-support.html'
            };
            
            if (serviceUrls[serviceType]) {
                window.location.href = serviceUrls[serviceType];
            }
        }
    </script>
</body>
</html>
